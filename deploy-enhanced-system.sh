#!/bin/bash

# 🚀 Enhanced HVAC CRM System Deployment Script
# Implements all technological advancements from the comprehensive system improvement plan

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.enhanced-system.yml"
ENV_FILE=".env.enhanced"
MONITORING_DIR="monitoring"
BACKUP_DIR="backups"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${PURPLE}================================${NC}"
    echo -e "${PURPLE}$1${NC}"
    echo -e "${PURPLE}================================${NC}"
}

# Check prerequisites
check_prerequisites() {
    log_header "🔍 CHECKING PREREQUISITES"
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    log_success "Docker is installed"
    
    # Check Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
    log_success "Docker Compose is installed"
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running. Please start Docker first."
        exit 1
    fi
    log_success "Docker daemon is running"
    
    # Check available disk space (minimum 10GB)
    available_space=$(df . | tail -1 | awk '{print $4}')
    if [ "$available_space" -lt 10485760 ]; then
        log_warning "Less than 10GB disk space available. Consider freeing up space."
    else
        log_success "Sufficient disk space available"
    fi
    
    # Check available memory (minimum 4GB)
    available_memory=$(free -m | awk 'NR==2{printf "%.0f", $7}')
    if [ "$available_memory" -lt 4096 ]; then
        log_warning "Less than 4GB memory available. System may run slowly."
    else
        log_success "Sufficient memory available"
    fi
}

# Create environment file
create_environment() {
    log_header "🔧 CREATING ENVIRONMENT CONFIGURATION"
    
    if [ ! -f "$ENV_FILE" ]; then
        log_info "Creating environment file: $ENV_FILE"
        cat > "$ENV_FILE" << EOF
# Enhanced HVAC CRM System Environment Configuration
# Generated on $(date)

# Security
JWT_SECRET=$(openssl rand -base64 32)
ENCRYPTION_KEY=$(openssl rand -base64 32)
GO_BACKEND_API_KEY=$(openssl rand -base64 24)

# Email Configuration
EMAIL_DOLORES_PASSWORD=Blaeritipol1
EMAIL_GRZEGORZ_PASSWORD=Blaeritipol1

# External APIs
TAVILY_API_KEY=your-tavily-api-key-here

# Database
POSTGRES_PASSWORD=blaeritipol

# Monitoring
PROMETHEUS_RETENTION=30d
GRAFANA_ADMIN_PASSWORD=admin

# Development
NODE_ENV=production
LOG_LEVEL=info
DEBUG=false

# Performance
MAX_WORKERS=4
CACHE_TTL=3600
BATCH_SIZE=100

# Features
ENABLE_CIRCUIT_BREAKER=true
ENABLE_PROMETHEUS=true
ENABLE_NATS=true
ENABLE_MCP=true
EOF
        log_success "Environment file created"
    else
        log_info "Environment file already exists"
    fi
}

# Create monitoring configuration
create_monitoring_config() {
    log_header "📊 CREATING MONITORING CONFIGURATION"
    
    mkdir -p "$MONITORING_DIR"
    
    # Prometheus configuration
    if [ ! -f "$MONITORING_DIR/prometheus.yml" ]; then
        log_info "Creating Prometheus configuration"
        cat > "$MONITORING_DIR/prometheus.yml" << EOF
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'hvac-go-backend'
    static_configs:
      - targets: ['go-backend:9090']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'hvac-python-mixer'
    static_configs:
      - targets: ['python-mixer:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'nats'
    static_configs:
      - targets: ['nats:8222']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
EOF
        log_success "Prometheus configuration created"
    fi
    
    # Grafana datasources
    mkdir -p "$MONITORING_DIR/grafana/datasources"
    if [ ! -f "$MONITORING_DIR/grafana/datasources/prometheus.yml" ]; then
        log_info "Creating Grafana datasource configuration"
        cat > "$MONITORING_DIR/grafana/datasources/prometheus.yml" << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
    editable: true
EOF
        log_success "Grafana datasource configuration created"
    fi
    
    # Grafana dashboards
    mkdir -p "$MONITORING_DIR/grafana/dashboards"
    if [ ! -f "$MONITORING_DIR/grafana/dashboards/dashboard.yml" ]; then
        log_info "Creating Grafana dashboard configuration"
        cat > "$MONITORING_DIR/grafana/dashboards/dashboard.yml" << EOF
apiVersion: 1

providers:
  - name: 'default'
    orgId: 1
    folder: ''
    type: file
    disableDeletion: false
    updateIntervalSeconds: 10
    allowUiUpdates: true
    options:
      path: /etc/grafana/provisioning/dashboards
EOF
        log_success "Grafana dashboard configuration created"
    fi
}

# Build and start services
deploy_services() {
    log_header "🚀 DEPLOYING ENHANCED HVAC CRM SYSTEM"
    
    log_info "Building Docker images..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" build --no-cache
    
    log_info "Starting infrastructure services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d postgres redis nats
    
    log_info "Waiting for infrastructure to be ready..."
    sleep 30
    
    log_info "Starting monitoring services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d prometheus grafana
    
    log_info "Starting MCP servers..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d mcp-memory mcp-tavily
    
    log_info "Waiting for MCP servers to be ready..."
    sleep 20
    
    log_info "Starting core application services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d go-backend python-mixer
    
    log_info "Starting frontend services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d hvac-frontend
    
    log_info "Starting utility services..."
    docker-compose -f "$COMPOSE_FILE" --env-file "$ENV_FILE" up -d health-monitor
    
    log_success "All services deployed successfully!"
}

# Verify deployment
verify_deployment() {
    log_header "✅ VERIFYING DEPLOYMENT"
    
    log_info "Checking service health..."
    
    # Wait for services to be fully ready
    sleep 60
    
    # Check Go Backend
    if curl -s http://localhost:8080/api/health > /dev/null; then
        log_success "Go Backend is healthy"
    else
        log_error "Go Backend is not responding"
    fi
    
    # Check Python Mixer
    if curl -s http://localhost:7860 > /dev/null; then
        log_success "Python Mixer is healthy"
    else
        log_error "Python Mixer is not responding"
    fi
    
    # Check Prometheus
    if curl -s http://localhost:9090/-/healthy > /dev/null; then
        log_success "Prometheus is healthy"
    else
        log_error "Prometheus is not responding"
    fi
    
    # Check Grafana
    if curl -s http://localhost:3001 > /dev/null; then
        log_success "Grafana is healthy"
    else
        log_error "Grafana is not responding"
    fi
    
    # Check NATS
    if curl -s http://localhost:8222/healthz > /dev/null; then
        log_success "NATS is healthy"
    else
        log_error "NATS is not responding"
    fi
    
    # Show running containers
    log_info "Running containers:"
    docker-compose -f "$COMPOSE_FILE" ps
}

# Show access information
show_access_info() {
    log_header "🌐 ACCESS INFORMATION"
    
    echo -e "${CYAN}Frontend Applications:${NC}"
    echo -e "  • HVAC Frontend:     ${GREEN}http://localhost:3000${NC}"
    echo -e "  • Python Mixer:      ${GREEN}http://localhost:7860${NC}"
    echo -e "  • Go Backend API:    ${GREEN}http://localhost:8080${NC}"
    echo ""
    echo -e "${CYAN}Monitoring & Admin:${NC}"
    echo -e "  • Grafana Dashboard: ${GREEN}http://localhost:3001${NC} (admin/admin)"
    echo -e "  • Prometheus:        ${GREEN}http://localhost:9090${NC}"
    echo -e "  • NATS Monitor:      ${GREEN}http://localhost:8222${NC}"
    echo ""
    echo -e "${CYAN}API Endpoints:${NC}"
    echo -e "  • Health Check:      ${GREEN}http://localhost:8080/api/health${NC}"
    echo -e "  • Transcription:     ${GREEN}http://localhost:8080/api/transcription${NC}"
    echo -e "  • MCP Memory:        ${GREEN}http://localhost:3001${NC}"
    echo -e "  • MCP Tavily:        ${GREEN}http://localhost:3002${NC}"
    echo ""
    echo -e "${CYAN}Database Connections:${NC}"
    echo -e "  • PostgreSQL:        ${GREEN}localhost:5432${NC} (hvacdb/blaeritipol)"
    echo -e "  • Redis:             ${GREEN}localhost:6379${NC}"
    echo -e "  • NATS:              ${GREEN}localhost:4222${NC}"
}

# Create backup
create_backup() {
    log_header "💾 CREATING SYSTEM BACKUP"
    
    mkdir -p "$BACKUP_DIR"
    backup_file="$BACKUP_DIR/hvac-backup-$(date +%Y%m%d-%H%M%S).tar.gz"
    
    log_info "Creating backup: $backup_file"
    
    # Stop services temporarily
    docker-compose -f "$COMPOSE_FILE" stop
    
    # Create backup
    tar -czf "$backup_file" \
        --exclude="node_modules" \
        --exclude="*.log" \
        --exclude="__pycache__" \
        --exclude=".git" \
        .
    
    # Restart services
    docker-compose -f "$COMPOSE_FILE" start
    
    log_success "Backup created: $backup_file"
}

# Main deployment function
main() {
    log_header "🚀 ENHANCED HVAC CRM SYSTEM DEPLOYMENT"
    echo -e "${CYAN}Implementing technological advancements:${NC}"
    echo -e "  ✓ Enhanced MCP Integration"
    echo -e "  ✓ NATS Message Queue"
    echo -e "  ✓ Circuit Breaker Pattern"
    echo -e "  ✓ Prometheus Monitoring"
    echo -e "  ✓ Secure Configuration"
    echo -e "  ✓ Go Backend Orchestration"
    echo ""
    
    check_prerequisites
    create_environment
    create_monitoring_config
    deploy_services
    verify_deployment
    show_access_info
    
    log_header "🎉 DEPLOYMENT COMPLETE"
    log_success "Enhanced HVAC CRM System is now running!"
    log_info "Check the logs with: docker-compose -f $COMPOSE_FILE logs -f"
    log_info "Stop the system with: docker-compose -f $COMPOSE_FILE down"
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        create_backup
        ;;
    "verify")
        verify_deployment
        ;;
    "info")
        show_access_info
        ;;
    "stop")
        log_info "Stopping Enhanced HVAC CRM System..."
        docker-compose -f "$COMPOSE_FILE" down
        log_success "System stopped"
        ;;
    "restart")
        log_info "Restarting Enhanced HVAC CRM System..."
        docker-compose -f "$COMPOSE_FILE" restart
        log_success "System restarted"
        ;;
    "logs")
        docker-compose -f "$COMPOSE_FILE" logs -f
        ;;
    *)
        echo "Usage: $0 {deploy|backup|verify|info|stop|restart|logs}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Deploy the complete enhanced system"
        echo "  backup   - Create a system backup"
        echo "  verify   - Verify deployment health"
        echo "  info     - Show access information"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  logs     - Show service logs"
        exit 1
        ;;
esac
