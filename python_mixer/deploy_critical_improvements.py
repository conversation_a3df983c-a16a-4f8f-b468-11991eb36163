#!/usr/bin/env python3
"""
🚀 CRITICAL IMPROVEMENTS DEPLOYMENT SCRIPT
Deploys the critical improvements to production environment
"""

import asyncio
import os
import sys
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

from loguru import logger


class CriticalImprovementsDeployment:
    """Deployment manager for critical improvements."""
    
    def __init__(self):
        self.deployment_steps = [
            "validate_environment",
            "backup_existing_config", 
            "install_dependencies",
            "update_configuration",
            "initialize_databases",
            "test_components",
            "start_services",
            "verify_deployment"
        ]
        
        self.deployment_status = {step: "pending" for step in self.deployment_steps}
        self.backup_dir = Path(f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        
    async def deploy(self) -> Dict[str, Any]:
        """Execute full deployment of critical improvements."""
        logger.info("🚀 Starting Critical Improvements Deployment")
        logger.info("=" * 60)
        
        deployment_start = datetime.now()
        
        try:
            # Execute deployment steps
            for step in self.deployment_steps:
                logger.info(f"📋 Executing: {step}")
                success = await getattr(self, step)()
                
                if success:
                    self.deployment_status[step] = "completed"
                    logger.success(f"✅ {step} completed")
                else:
                    self.deployment_status[step] = "failed"
                    logger.error(f"❌ {step} failed")
                    return self._generate_deployment_report(deployment_start, False)
            
            deployment_end = datetime.now()
            duration = (deployment_end - deployment_start).total_seconds()
            
            logger.success(f"🎉 Deployment completed successfully in {duration:.2f}s")
            return self._generate_deployment_report(deployment_start, True)
            
        except Exception as e:
            logger.error(f"💥 Deployment failed with exception: {e}")
            return self._generate_deployment_report(deployment_start, False, str(e))
    
    async def validate_environment(self) -> bool:
        """Validate deployment environment."""
        try:
            # Check Python version
            if sys.version_info < (3, 8):
                logger.error("Python 3.8+ required")
                return False
            
            # Check required directories
            required_dirs = [
                "core/security",
                "core/queues", 
                "core/validation",
                "core/integration"
            ]
            
            for dir_path in required_dirs:
                if not Path(dir_path).exists():
                    logger.error(f"Required directory missing: {dir_path}")
                    return False
            
            # Check environment variables template
            if not Path(".env.template").exists():
                logger.warning(".env.template not found")
            
            logger.info("✅ Environment validation passed")
            return True
            
        except Exception as e:
            logger.error(f"Environment validation failed: {e}")
            return False
    
    async def backup_existing_config(self) -> bool:
        """Backup existing configuration files."""
        try:
            self.backup_dir.mkdir(exist_ok=True)
            
            # Files to backup
            backup_files = [
                "config.yaml",
                "config_loader.py",
                "sync_manager.db"
            ]
            
            for file_name in backup_files:
                file_path = Path(file_name)
                if file_path.exists():
                    backup_path = self.backup_dir / file_name
                    shutil.copy2(file_path, backup_path)
                    logger.info(f"📦 Backed up: {file_name}")
            
            logger.info(f"✅ Backup created in: {self.backup_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Backup failed: {e}")
            return False
    
    async def install_dependencies(self) -> bool:
        """Install required dependencies."""
        try:
            # Check if we're in a virtual environment
            if not hasattr(sys, 'real_prefix') and not (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
                logger.warning("Not in a virtual environment - proceeding anyway")
            
            # Required packages for critical improvements
            required_packages = [
                "redis",
                "aioredis", 
                "pydantic",
                "aiosqlite",
                "python-dotenv",
                "loguru"
            ]
            
            logger.info("📦 Checking required packages...")
            
            missing_packages = []
            for package in required_packages:
                try:
                    __import__(package.replace("-", "_"))
                except ImportError:
                    missing_packages.append(package)
            
            if missing_packages:
                logger.warning(f"Missing packages: {missing_packages}")
                logger.info("Please install with: pip install " + " ".join(missing_packages))
                # In production, you might want to auto-install
                # subprocess.run([sys.executable, "-m", "pip", "install"] + missing_packages)
            
            logger.info("✅ Dependencies check completed")
            return True
            
        except Exception as e:
            logger.error(f"Dependencies installation failed: {e}")
            return False
    
    async def update_configuration(self) -> bool:
        """Update configuration files."""
        try:
            # Check if .env file exists
            env_file = Path(".env")
            if not env_file.exists():
                logger.warning(".env file not found")
                logger.info("Please create .env file from .env.template")
                logger.info("Set all required environment variables before proceeding")
                
                # Create a basic .env file with placeholders
                with open(".env", "w") as f:
                    f.write("# Environment variables for python_mixer\n")
                    f.write("# Please fill in actual values\n\n")
                    f.write("DOLORES_EMAIL_PASSWORD=your_password_here\n")
                    f.write("GRZEGORZ_EMAIL_PASSWORD=your_password_here\n")
                    f.write("POSTGRES_USER=your_user_here\n")
                    f.write("POSTGRES_PASSWORD=your_password_here\n")
                    f.write("MINIO_ACCESS_KEY=your_access_key_here\n")
                    f.write("MINIO_SECRET_KEY=your_secret_key_here\n")
                
                logger.info("📝 Created basic .env file - please update with actual values")
            
            # Validate configuration loading
            try:
                from core.security import env_loader
                env_loader.load_environment()
                logger.info("✅ Configuration loading validated")
            except Exception as e:
                logger.warning(f"Configuration validation warning: {e}")
            
            return True
            
        except Exception as e:
            logger.error(f"Configuration update failed: {e}")
            return False
    
    async def initialize_databases(self) -> bool:
        """Initialize required databases."""
        try:
            # Initialize sync manager database
            from core.integration.sync_manager import sync_manager
            await sync_manager.initialize()
            logger.info("✅ Sync manager database initialized")
            
            # Test Redis connection (if available)
            try:
                from core.queues import queue_manager
                test_queue = await queue_manager.get_queue("deployment_test")
                logger.info("✅ Redis connection tested")
            except Exception as e:
                logger.warning(f"Redis connection test failed: {e}")
                logger.info("Redis is optional but recommended for production")
            
            return True
            
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            return False
    
    async def test_components(self) -> bool:
        """Test critical improvement components."""
        try:
            # Test security module
            from core.security import env_loader
            summary = env_loader.get_environment_summary()
            logger.info("✅ Security module tested")
            
            # Test validation module
            from core.validation import unified_validator
            metrics = unified_validator.get_validation_metrics()
            logger.info("✅ Validation module tested")
            
            logger.info("✅ Component testing completed")
            return True
            
        except Exception as e:
            logger.error(f"Component testing failed: {e}")
            return False
    
    async def start_services(self) -> bool:
        """Start critical improvement services."""
        try:
            logger.info("🔄 Services are ready to start")
            logger.info("Use the following commands to start services:")
            logger.info("  - python launch_enhanced_python_mixer.py")
            logger.info("  - python test_critical_improvements.py")
            
            return True
            
        except Exception as e:
            logger.error(f"Service startup failed: {e}")
            return False
    
    async def verify_deployment(self) -> bool:
        """Verify deployment success."""
        try:
            # Run basic verification tests
            logger.info("🔍 Running deployment verification...")
            
            # Check all modules can be imported
            modules_to_check = [
                "core.security",
                "core.queues", 
                "core.validation",
                "core.integration.sync_manager"
            ]
            
            for module in modules_to_check:
                try:
                    __import__(module)
                    logger.info(f"✅ {module} imported successfully")
                except ImportError as e:
                    logger.error(f"❌ Failed to import {module}: {e}")
                    return False
            
            logger.info("✅ Deployment verification passed")
            return True
            
        except Exception as e:
            logger.error(f"Deployment verification failed: {e}")
            return False
    
    def _generate_deployment_report(self, start_time: datetime, success: bool, error: str = None) -> Dict[str, Any]:
        """Generate deployment report."""
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        report = {
            "deployment_id": f"critical_improvements_{start_time.strftime('%Y%m%d_%H%M%S')}",
            "timestamp": start_time.isoformat(),
            "duration_seconds": duration,
            "success": success,
            "error": error,
            "steps": self.deployment_status,
            "backup_location": str(self.backup_dir),
            "next_steps": [
                "Set environment variables in .env file",
                "Run test_critical_improvements.py to validate",
                "Start services with launch_enhanced_python_mixer.py",
                "Monitor logs for any issues"
            ] if success else [
                "Check error logs",
                "Restore from backup if needed",
                "Fix issues and retry deployment"
            ]
        }
        
        return report


async def main():
    """Execute critical improvements deployment."""
    logger.info("🚀 PYTHON MIXER CRITICAL IMPROVEMENTS DEPLOYMENT")
    logger.info("=" * 60)
    
    deployment = CriticalImprovementsDeployment()
    report = await deployment.deploy()
    
    # Save deployment report
    import json
    report_file = Path(f"deployment_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2)
    
    logger.info(f"📊 Deployment report saved to: {report_file}")
    
    if report["success"]:
        logger.success("🎉 CRITICAL IMPROVEMENTS DEPLOYED SUCCESSFULLY!")
        logger.info("\n📋 NEXT STEPS:")
        for step in report["next_steps"]:
            logger.info(f"  • {step}")
    else:
        logger.error("💥 DEPLOYMENT FAILED!")
        logger.info("\n🔧 TROUBLESHOOTING:")
        for step in report["next_steps"]:
            logger.info(f"  • {step}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
