import yaml
from pathlib import Path
import os
import re
from typing import Any, Dict, Optional
from loguru import logger

class Config:
    _instance = None
    _config_data = None

    def __new__(cls, config_path: Path = Path(__file__).parent / "config.yaml"):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
            cls._instance._load_config(config_path)
        return cls._instance

    def _load_config(self, config_path: Path):
        if not config_path.exists():
            logger.error(f"Configuration file not found at {config_path}")
            raise FileNotFoundError(f"Configuration file not found at {config_path}")

        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                raw_config = f.read()

            # Process environment variable substitutions
            processed_config = self._substitute_env_vars(raw_config)
            self._config_data = yaml.safe_load(processed_config)

            logger.info(f"Configuration loaded from {config_path}")

            # Validate required environment variables
            self._validate_required_env_vars()

        except yaml.YAMLError as e:
            logger.error(f"Error parsing YAML configuration file: {e}")
            raise
        except Exception as e:
            logger.error(f"An unexpected error occurred while loading configuration: {e}")
            raise

    def _substitute_env_vars(self, config_text: str) -> str:
        """Substitute environment variables in config text."""
        # Pattern: ${VAR_NAME} or ${VAR_NAME:-default_value}
        pattern = r'\$\{([^}]+)\}'

        def replace_var(match):
            var_expr = match.group(1)
            if ':-' in var_expr:
                var_name, default_value = var_expr.split(':-', 1)
                return os.getenv(var_name.strip(), default_value.strip())
            else:
                var_name = var_expr.strip()
                value = os.getenv(var_name)
                if value is None:
                    logger.error(f"Required environment variable '{var_name}' is not set")
                    raise ValueError(f"Required environment variable '{var_name}' is not set")
                return value

        return re.sub(pattern, replace_var, config_text)

    def _validate_required_env_vars(self):
        """Validate that all required environment variables are set."""
        required_vars = [
            'DOLORES_EMAIL_PASSWORD',
            'GRZEGORZ_EMAIL_PASSWORD',
            'POSTGRES_USER',
            'POSTGRES_PASSWORD',
            'MINIO_ACCESS_KEY',
            'MINIO_SECRET_KEY'
        ]

        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)

        if missing_vars:
            logger.error(f"Missing required environment variables: {', '.join(missing_vars)}")
            logger.error("Please set these environment variables before running the application")
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

        logger.success("✅ All required environment variables are set")

    def get(self, key: str, default: Any = None) -> Any:
        """
        Retrieves a configuration value using a dot-separated key (e.g., "email.dolores.username").
        """
        keys = key.split('.')
        value = self._config_data
        for k in keys:
            if isinstance(value, dict):
                value = value.get(k)
            else:
                return default # Key path does not exist
            if value is None:
                return default
        return value

    def get_email_config(self, account_name: str) -> Dict[str, Any]:
        """Retrieves email configuration for a specific account, prioritizing environment variables."""
        config = self.get(f"email.{account_name}", {})
        
        # Override with environment variables if available
        config['username'] = os.getenv(f"{account_name.upper()}_EMAIL_USERNAME", config.get('username'))
        config['password'] = os.getenv(f"{account_name.upper()}_EMAIL_PASSWORD", config.get('password'))
        
        return config

    def get_service_url(self, service_name: str) -> Optional[str]:
        """Retrieves the URL for a specific service."""
        return self.get(f"services.{service_name}")

    def get_test_setting(self, setting_name: str) -> Any:
        """Retrieves a specific test setting."""
        return self.get(f"test_settings.{setting_name}")

# Initialize the configuration instance
config = Config()

# Example usage (for testing purposes)
if __name__ == "__main__":
    try:
        logger.info("--- Testing Config Loader ---")

        # Accessing email config
        dolores_email = config.get_email_config("dolores")
        logger.info(f"Dolores Email Host: {dolores_email.get('host')}")
        logger.info(f"Dolores Email Username: {dolores_email.get('username')}")
        # logger.info(f"Dolores Email Password: {dolores_email.get('password')}") # Don't log passwords in real apps!

        # Accessing service URLs
        nemo_url = config.get_service_url("nemo_stt")
        logger.info(f"NeMo STT URL: {nemo_url}")

        # Accessing test settings
        target_time = config.get_test_setting("target_processing_time")
        logger.info(f"Target Processing Time: {target_time}")

        # Test non-existent key
        non_existent = config.get("non.existent.key", "default_value")
        logger.info(f"Non-existent key test: {non_existent}")

    except Exception as e:
        logger.error(f"Error during config loader test: {e}")