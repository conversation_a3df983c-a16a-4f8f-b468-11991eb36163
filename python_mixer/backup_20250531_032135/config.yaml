# Centralized Configuration for python_mixer
# SECURITY: Sensitive data moved to environment variables

email:
  dolores:
    host: serwer2440139.home.pl
    port: 993
    username: <EMAIL>
    password: ${DOLORES_EMAIL_PASSWORD}  # Set via environment variable
    use_ssl: true
  grz<PERSON>rz:
    host: serwer2440139.home.pl
    port: 993
    username: g<PERSON><PERSON><PERSON>@koldbringers.pl
    password: ${GRZEGORZ_EMAIL_PASSWORD}  # Set via environment variable
    use_ssl: true

services:
  nemo_stt: ${NEMO_STT_URL:-http://localhost:8889}
  transcription_orchestrator: ${TRANSCRIPTION_ORCHESTRATOR_URL:-http://localhost:9000}
  gemma_integration: ${GEMMA_INTEGRATION_URL:-http://*************:1234}  # LM Studio
  postgres: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST:-**************}:${POSTGRES_PORT:-5432}/${POSTGRES_DB:-hvac_crm}

test_settings:
  target_processing_time: 30.0 # seconds
  target_accuracy: 0.95 # 95%
  target_confidence: 0.80 # 80%
  max_test_files: 10
  test_timeout: 300 # 5 minutes per test
  max_emails_per_batch: 50 # Maximum emails to process in a single batch

# MinIO Object Storage (GoBackend-Kratos Production)
minio:
  endpoint: ${MINIO_ENDPOINT:-**************:9000}
  access_key_id: ${MINIO_ACCESS_KEY}  # Set via environment variable
  secret_access_key: ${MINIO_SECRET_KEY}  # Set via environment variable
  use_ssl: ${MINIO_USE_SSL:-false}
  region: ${MINIO_REGION:-us-east-1}
  default_bucket: ${MINIO_DEFAULT_BUCKET:-dolores-attachments}

  # Enhanced Storage Configuration
  buckets:
    audio: "hvac-audio-files"
    documents: "hvac-documents"
    transcriptions: "hvac-transcriptions"
    processed: "hvac-processed-files"
    temp: "hvac-temp-files"

  # Performance settings
  part_size: 67108864        # 64MB
  max_retries: 3
  connect_timeout: "30s"
  request_timeout: "5m"

  # File processing settings
  max_file_size: 104857600   # 100MB
  supported_formats: ["m4a", "mp3", "wav", "mp4"]

  # Lifecycle settings
  temp_file_retention_days: 7
  processed_file_retention_days: 365