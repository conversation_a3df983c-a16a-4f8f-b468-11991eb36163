# Environment Variables Template for python_mixer
# Copy this file to .env and fill in the actual values
# NEVER commit .env file to version control!

# Email Configuration
DOLORES_EMAIL_PASSWORD=your_dolores_password_here
GRZEGORZ_EMAIL_PASSWORD=your_grzegorz_password_here

# Database Configuration
POSTGRES_USER=your_postgres_user
POSTGRES_PASSWORD=your_postgres_password
POSTGRES_HOST=**************
POSTGRES_PORT=5432
POSTGRES_DB=hvac_crm

# MinIO Object Storage
MINIO_ACCESS_KEY=your_minio_access_key
MINIO_SECRET_KEY=your_minio_secret_key
MINIO_ENDPOINT=**************:9000
MINIO_USE_SSL=false
MINIO_REGION=us-east-1
MINIO_DEFAULT_BUCKET=dolores-attachments

# Service URLs (optional - defaults provided in config.yaml)
NEMO_STT_URL=http://localhost:8889
TRANSCRIPTION_ORCHESTRATOR_URL=http://localhost:9000
GEMMA_INTEGRATION_URL=http://*************:1234

# Go Backend Configuration
GO_BACKEND_URL=http://localhost:8080
GO_BACKEND_API_KEY=your_api_key_here

# MCP Server Configuration
MCP_MEMORY_URL=http://localhost:3001
MCP_TAVILY_URL=http://localhost:3002

# Redis Configuration (for persistent queues)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# Monitoring & Observability
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=9090
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true

# Security
JWT_SECRET_KEY=your_jwt_secret_key_here
API_RATE_LIMIT=100
ENABLE_CORS=true
