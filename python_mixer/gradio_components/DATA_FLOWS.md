# Data Flows in `python_mixer/gradio_components`

This document provides a comprehensive overview and detailed breakdown of the data flows within the `python_mixer/gradio_components` package. The goal is to enhance human comprehension of how data is processed, transformed, and exchanged between different components.

## 1. High-Level Data Flow Overview

The `gradio_components` package integrates several key functionalities, primarily focusing on Invoice Processing, Email Analysis, and Calendar Management. The `DocumentProcessor` acts as a central utility for handling document-related tasks, while `InvoiceProcessingHelpers` provides specialized logic for invoice data enhancement.

```mermaid
graph TD
    subgraph User Interaction
        UI[Gradio User Interface]
    end

    subgraph Core Components
        EAC[EmailAnalysisComponent]
        IPC[InvoiceProcessingComponent]
        CMC[CalendarManagementComponent]
    end

    subgraph Utility & Logic
        DP[DocumentProcessor]
        IPH[InvoiceProcessingHelpers]
    end

    subgraph External Systems / Data Stores
        DB[(Internal Financial Data / CRM)]
        AI[AI Models (Simulated)]
    end

    UI --> EAC;
    UI --> IPC;
    UI --> CMC;

    EAC -- Email Content, PDF Attachment --> DP;
    IPC -- Invoice File --> DP;

    DP -- Extracted Text, Classified Doc --> IPH;
    DP -- Extracted Text, Classified Doc --> EAC;

    IPH -- Enhanced Invoice Data --> IPC;

    IPC -- Processed Invoice Data --> DB;
    EAC -- Analysis Results --> DB;

    EAC -- Simulated AI Analysis --> AI;
    AI -- Analysis Results --> EAC;

    CMC -- Appointment Details --> DB;
    CMC -- Route Optimization Request --> AI;
    AI -- Optimized Routes --> CMC;

    style UI fill:#f9f,stroke:#333,stroke-width:2px
    style EAC fill:#bbf,stroke:#333,stroke-width:2px
    style IPC fill:#bbf,stroke:#333,stroke-width:2px
    style CMC fill:#bbf,stroke:#333,stroke-width:2px
    style DP fill:#bfb,stroke:#333,stroke-width:2px
    style IPH fill:#bfb,stroke:#333,stroke-width:2px
    style DB fill:#ffb,stroke:#333,stroke-width:2px
    style AI fill:#fbb,stroke:#333,stroke-width:2px
```

**Explanation:**

*   **User Interface (UI)**: The Gradio interface serves as the primary interaction point for users, allowing them to input data and view results.
*   **`EmailAnalysisComponent` (EAC)**: Processes email content and attachments, leveraging `DocumentProcessor` for PDF analysis and interacting with (simulated) AI models for advanced insights.
*   **`InvoiceProcessingComponent` (IPC)**: Handles invoice file uploads, uses `DocumentProcessor` for initial extraction, and `InvoiceProcessingHelpers` for data enhancement before saving to internal data stores.
*   **`CalendarManagementComponent` (CMC)**: Manages appointment scheduling and technician assignments, interacting with internal data stores and (simulated) AI for route optimization.
*   **`DocumentProcessor` (DP)**: A core utility responsible for extracting text and classifying documents (PDFs, images). It's used by both `EmailAnalysisComponent` and `InvoiceProcessingComponent`.
*   **`InvoiceProcessingHelpers` (IPH)**: Provides helper functions specifically for enhancing and summarizing invoice data, used by `InvoiceProcessingComponent`.
*   **Internal Financial Data / CRM (DB)**: Represents the persistent storage for processed invoices, email analysis results, and appointment data.
*   **AI Models (Simulated)**: Placeholder for external AI services or internal AI logic for tasks like email analysis and route optimization.

## 2. Detailed Component Data Flows

### 2.1. `DocumentProcessor` Data Flow

The `DocumentProcessor` is responsible for transforming raw document files into structured text and initial data extractions.

```mermaid
graph TD
    A[process_document(file_content, filename)] --> B{Determine File Type (PDF/Image)};
    B -- PDF --> C[extract_pdf_text(pdf_content)];
    B -- Image --> D[extract_image_text(image_content)];

    C -- Text (pdfplumber/PyPDF2) --> E{Text Extracted?};
    E -- No / Low Text --> F[pdf_to_ocr(pdf_content)];
    F --> G[OCR Extracted Text];
    E -- Yes --> H[Extracted Text];

    D --> I[OCR Extracted Text];

    G --> J[Combined Extracted Text];
    H --> J;
    I --> J;

    J --> K[analyze_document_content(text)];
    K --> L[classify_document(text)];
    K --> M[extract_invoice_data(text)];
    K --> N[identify_supplier(text, invoice_data)];
    K --> O[extract_metadata(text)];

    M -- Raw Invoice Data --> P[process_line_items];
    M -- Raw Invoice Data --> Q[post_process_invoice_data];

    P --> R[Structured Line Items];
    Q --> S[Cleaned Invoice Data];

    L --> T[Document Type, Confidence];
    R --> U[Final Invoice Data];
    S --> U;
    N --> V[Supplier Info];
    O --> W[General Metadata];

    K --> X[Return: Document Type, Confidence, Invoice Data, Supplier Info, Metadata, Extracted Text];

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style E fill:#f9f,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
    style G fill:#bfb,stroke:#333,stroke-width:2px
    style H fill:#bfb,stroke:#333,stroke-width:2px
    style I fill:#bfb,stroke:#333,stroke-width:2px
    style J fill:#bfb,stroke:#333,stroke-width:2px
    style K fill:#bbf,stroke:#333,stroke-width:2px
    style L fill:#bfb,stroke:#333,stroke-width:2px
    style M fill:#bfb,stroke:#333,stroke-width:2px
    style N fill:#bfb,stroke:#333,stroke-width:2px
    style O fill:#bfb,stroke:#333,stroke-width:2px
    style P fill:#bfb,stroke:#333,stroke-width:2px
    style Q fill:#bfb,stroke:#333,stroke-width:2px
    style R fill:#bfb,stroke:#333,stroke-width:2px
    style S fill:#bfb,stroke:#333,stroke-width:2px
    style T fill:#bfb,stroke:#333,stroke-width:2px
    style U fill:#bfb,stroke:#333,stroke-width:2px
    style V fill:#bfb,stroke:#333,stroke-width:2px
    style W fill:#bfb,stroke:#333,stroke-width:2px
    style X fill:#f9f,stroke:#333,stroke-width:2px
```

### 2.2. `InvoiceProcessingComponent` and `InvoiceProcessingHelpers` Data Flow

This section details how the `InvoiceProcessingComponent` orchestrates the invoice processing workflow, heavily relying on `InvoiceProcessingHelpers` for data enrichment and presentation.

```mermaid
graph TD
    A[InvoiceProcessingComponent.create_interface()] --> B[User Uploads Invoice File];
    B -- invoice_file, options --> C[IPC._process_invoice()];

    C -- file_content, filename --> D[DocumentProcessor.process_document()];
    D -- result (raw extracted data) --> E[IPH.enhance_invoice_results(result, options)];

    E -- enhanced_result --> F[IPH.generate_supplier_info(enhanced_result)];
    E -- enhanced_result --> G[IPH.generate_financial_analysis(enhanced_result, customer, category)];
    E -- enhanced_result --> H[IPH.create_financial_chart(enhanced_result)];

    F --> I[Supplier Info HTML];
    G --> J[Financial Analysis HTML];
    H --> K[Plotly Financial Chart];

    C -- status, enhanced_result, I, J, K --> L[Update Gradio UI];

    L -- User Clicks Save --> M[IPC._save_invoice_to_system(invoice_results, customer, category)];
    M -- invoice_record --> N[IPC._update_financial_data(invoice_record)];
    N --> O[Internal Financial Data (self.financial_data)];
    M --> P[Update Gradio UI (History, Monthly Stats)];

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bfb,stroke:#333,stroke-width:2px
    style E fill:#bfb,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
    style G fill:#bfb,stroke:#333,stroke-width:2px
    style H fill:#bfb,stroke:#333,stroke-width:2px
    style I fill:#bfb,stroke:#333,stroke-width:2px
    style J fill:#bfb,stroke:#333,stroke-width:2px
    style K fill:#bfb,stroke:#333,stroke-width:2px
    style L fill:#f9f,stroke:#333,stroke-width:2px
    style M fill:#bbf,stroke:#333,stroke-width:2px
    style N fill:#bfb,stroke:#333,stroke-width:2px
    style O fill:#ffb,stroke:#333,stroke-width:2px
    style P fill:#f9f,stroke:#333,stroke-width:2px
```

### 2.3. `EmailAnalysisComponent` Data Flow

The `EmailAnalysisComponent` processes email content and attachments, providing AI-driven insights and recommendations.

```mermaid
graph TD
    A[EmailAnalysisComponent.create_interface()] --> B[User Inputs Email Content / Attaches PDF];
    B -- email_content, options, pdf_attachment --> C[EAC._analyze_email_enhanced()];

    C -- email_content, options --> D[EAC._simulate_ai_analysis()];
    D -- simulated_analysis_result --> E[Analysis Results (JSON)];

    C -- pdf_attachment, process_pdf --> F[EAC._process_pdf_attachment()];
    F -- file_content, filename --> G[DocumentProcessor.process_document()];
    G -- pdf_result --> H[EAC._calculate_pdf_financial_summary()];
    H --> I[PDF Analysis Result (enhanced)];

    E --> J[EAC._generate_customer_insights(analysis_result)];
    E --> K[EAC._generate_recommendations(analysis_result)];
    E --> L[EAC._create_analysis_metrics_plot(analysis_result)];

    J --> M[Customer Insights Markdown];
    K --> N[Recommended Actions Markdown];
    L --> O[Plotly Metrics Chart];

    C -- status, E, M, N, O --> P[Update Gradio UI];

    style A fill:#f9f,stroke:#333,stroke-width:2px
    style B fill:#f9f,stroke:#333,stroke-width:2px
    style C fill:#bbf,stroke:#333,stroke-width:2px
    style D fill:#bfb,stroke:#333,stroke-width:2px
    style E fill:#bfb,stroke:#333,stroke-width:2px
    style F fill:#bfb,stroke:#333,stroke-width:2px
    style G fill:#bfb,stroke:#333,stroke-width:2px
    style H fill:#bfb,stroke:#333,stroke-width:2px
    style I fill:#bfb,stroke:#333,stroke-width:2px
    style J fill:#bfb,stroke:#333,stroke-width:2px
    style K fill:#bfb,stroke:#333,stroke-width:2px
    style L fill:#bfb,stroke:#333,stroke-width:2px
    style M fill:#bfb,stroke:#333,stroke-width:2px
    style N fill:#bfb,stroke:#333,stroke-width:2px
    style O fill:#bfb,stroke:#333,stroke-width:2px
    style P fill:#f9f,stroke:#333,stroke-width:2px
```

## 3. Key Data Structures

This section defines the structure of critical data dictionaries passed between components.

### 3.1. `DocumentProcessor.process_document()` Output (`result`)

This dictionary contains the raw extracted data from a document, before further enhancement.

```json
{
    "filename": "example_invoice.pdf",
    "file_size": 123456,
    "processed_at": "2024-05-31T10:30:00.000Z",
    "success": true,
    "document_type": "invoice",
    "extracted_text": "Full text content of the document...",
    "extraction_method": "pdfplumber",
    "text_length": 5000,
    "metadata": {
        "text_length": 5000,
        "word_count": 800,
        "language": "polish",
        "contains_amounts": true,
        "contains_dates": true,
        "contains_tax_id": true
    },
    "invoice_data": {
        "invoice_number": "FV/2024/001",
        "date": "2024-05-15",
        "total_amount": 1234.56,
        "currency": "PLN",
        "tax_id": "PL123-456-78-90",
        "vendor_name": "ABC Sp. z o.o.",
        "line_items": [
            {
                "position": "1",
                "description": "Klimatyzator LG",
                "quantity": 1.0,
                "unit_price": 1000.00,
                "total_price": 1000.00
            },
            {
                "position": "2",
                "description": "Montaż",
                "quantity": 1.0,
                "unit_price": 200.00,
                "total_price": 200.00
            }
        ],
        "payment_terms": "30 dni",
        "extracted_fields": {}
    },
    "supplier_info": {
        "identified": true,
        "supplier_id": "lg",
        "supplier_name": "LG Electronics",
        "category": "equipment_manufacturer",
        "confidence": 0.95,
        "match_method": "tax_id"
    },
    "confidence_score": 0.85
}
```

### 3.2. `InvoiceProcessingHelpers.enhance_invoice_results()` Output (`enhanced_result`)

This dictionary builds upon the `DocumentProcessor` output by adding categorization, supplier validation, and financial summaries.

```json
{
    "filename": "example_invoice.pdf",
    "file_size": 123456,
    "processed_at": "2024-05-31T10:30:00.000Z",
    "success": true,
    "document_type": "invoice",
    "extracted_text": "Full text content of the document...",
    "extraction_method": "pdfplumber",
    "text_length": 5000,
    "metadata": {
        "text_length": 5000,
        "word_count": 800,
        "language": "polish",
        "contains_amounts": true,
        "contains_dates": true,
        "contains_tax_id": true
    },
    "invoice_data": {
        "invoice_number": "FV/2024/001",
        "date": "2024-05-15",
        "total_amount": 1234.56,
        "currency": "PLN",
        "tax_id": "PL123-456-78-90",
        "vendor_name": "ABC Sp. z o.o.",
        "line_items": [
            {
                "position": "1",
                "description": "Klimatyzator LG",
                "quantity": 1.0,
                "unit_price": 1000.00,
                "total_price": 1000.00
            },
            {
                "position": "2",
                "description": "Montaż",
                "quantity": 1.0,
                "unit_price": 200.00,
                "total_price": 200.00
            }
        ],
        "payment_terms": "30 dni",
        "extracted_fields": {}
    },
    "supplier_info": {
        "identified": true,
        "supplier_id": "lg",
        "supplier_name": "LG Electronics",
        "category": "equipment_manufacturer",
        "confidence": 0.95,
        "match_method": "tax_id"
    },
    "confidence_score": 0.85,
    "processing_options": {
        "auto_categorize": true,
        "validate_supplier": true,
        "enhanced_at": "2024-05-31T10:30:05.000Z"
    },
    "auto_category": {
        "category": "🔧 Sprzęt HVAC",
        "confidence": 0.8,
        "reasoning": ["Dostawca sprzętu HVAC", "Słowa kluczowe: klimatyzator"]
    },
    "supplier_validation": {
        "is_valid": true,
        "warnings": [],
        "recommendations": ["Autoryzowany partner - sprawdź warunki gwarancji"]
    },
    "financial_summary": {
        "total_amount": 1234.56,
        "currency": "PLN",
        "line_items_count": 2,
        "average_item_value": 617.28,
        "tax_amount": 231.56,
        "net_amount": 1003.00
    }
}
```

### 3.3. `EmailAnalysisComponent._simulate_ai_analysis()` Output (`analysis_result`)

This dictionary contains the simulated AI analysis results for an email.

```json
{
    "email_metadata": {
        "from": "<EMAIL>",
        "subject": "Awaria klimatyzacji LG - pilne wsparcie",
        "timestamp": "2024-05-31T10:30:10.000Z",
        "framework_used": "🧠 LangGraph (Zalecane)",
        "processing_time_ms": 850
    },
    "sentiment_analysis": {
        "score": -0.6,
        "label": "Negatywny",
        "confidence": 0.85
    },
    "entities": [
        {"type": "EQUIPMENT", "value": "LG", "confidence": 0.95},
        {"type": "MODEL", "value": "S12ET", "confidence": 0.90},
        {"type": "LOCATION", "value": "Warszawa", "confidence": 0.85},
        {"type": "PHONE", "value": "+48 ***********", "confidence": 0.95}
    ],
    "intent_classification": {
        "category": "SERVICE_REQUEST",
        "confidence": 0.90,
        "description": "Zgłoszenie serwisowe"
    },
    "priority_assessment": {
        "level": "HIGH",
        "score": 0.9,
        "description": "Wysoki priorytet - wymaga szybkiej reakcji"
    },
    "pdf_attachment": {
        "success": true,
        "filename": "invoice_from_email.pdf",
        "document_type": "invoice",
        "confidence_score": 0.92,
        "extracted_text_length": 1500,
        "extraction_method": "pdfplumber",
        "email_context": {
            "is_invoice": true,
            "is_quote": false,
            "is_service_report": false,
            "contains_financial_data": true,
            "supplier_identified": true
        },
        "invoice_data": {
            "invoice_number": "FV/2024/001",
            "date": "2024-05-15",
            "total_amount": 1234.56,
            "currency": "PLN",
            "tax_id": "PL123-456-78-90",
            "vendor_name": "ABC Sp. z o.o.",
            "line_items": [
                {
                    "position": "1",
                    "description": "Klimatyzator LG",
                    "quantity": 1.0,
                    "unit_price": 1000.00,
                    "total_price": 1000.00
                }
            ],
            "payment_terms": "30 dni",
            "extracted_fields": {}
        },
        "supplier_info": {
            "identified": true,
            "supplier_id": "lg",
            "supplier_name": "LG Electronics",
            "category": "equipment_manufacturer",
            "confidence": 0.95,
            "match_method": "tax_id"
        },
        "financial_summary": {
            "total_amount": 1234.56,
            "currency": "PLN",
            "invoice_number": "FV/2024/001",
            "invoice_date": "2024-05-15",
            "vendor_name": "ABC Sp. z o.o.",
            "line_items_count": 1,
            "has_financial_data": true
        },
        "metadata": {
            "text_length": 1500,
            "word_count": 250,
            "language": "polish",
            "contains_amounts": true,
            "contains_dates": true,
            "contains_tax_id": true
        }
    }
}
```

### 3.4. `InvoiceProcessingComponent` Internal Financial Data (`self.financial_data`)

This dictionary stores aggregated financial metrics over time.

```json
{
    "monthly_expenses": {
        "2024-05": 5000.00,
        "2024-04": 3500.00
    },
    "supplier_totals": {
        "LG Electronics": 2500.00,
        "Elektro-Parts Sp. z o.o.": 1000.00
    },
    "category_breakdown": {
        "🔧 Sprzęt HVAC": 3000.00,
        "🔩 Części zamienne": 1500.00,
        "⚡ Serwis i naprawy": 500.00
    },
    "customer_costs": {
        "Jan Kowalski - ul. Testowa 123": 1200.00,
        "Firma ABC Sp. z o.o.": 800.00
    },
    "total_processed": 15
}