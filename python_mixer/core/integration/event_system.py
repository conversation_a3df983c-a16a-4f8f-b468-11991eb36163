#!/usr/bin/env python3
"""
⚡ REAL-TIME EVENT SYSTEM
System zdarzeń w czasie rzeczywistym dla integracji Python Mixer ↔ GoBackend
Zapewnia natychmiastową komunikację i synchronizację stanów
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Set
from dataclasses import dataclass, asdict
from enum import Enum
import uuid
import weakref
from collections import defaultdict
import websockets
import redis.asyncio as aioredis

logger = logging.getLogger(__name__)

class EventType(Enum):
    """Typy zdarzeń w systemie"""
    # Email Events
    EMAIL_RECEIVED = "email.received"
    EMAIL_PROCESSED = "email.processed"
    ATTACHMENT_EXTRACTED = "attachment.extracted"
    
    # Transcription Events
    TRANSCRIPTION_STARTED = "transcription.started"
    TRANSCRIPTION_COMPLETED = "transcription.completed"
    TRANSCRIPTION_FAILED = "transcription.failed"
    
    # Sync Events
    SYNC_STARTED = "sync.started"
    SYNC_COMPLETED = "sync.completed"
    SYNC_FAILED = "sync.failed"
    
    # Service Events
    SERVICE_ONLINE = "service.online"
    SERVICE_OFFLINE = "service.offline"
    SERVICE_DEGRADED = "service.degraded"
    
    # Customer Events
    CUSTOMER_CREATED = "customer.created"
    CUSTOMER_UPDATED = "customer.updated"
    
    # Equipment Events
    EQUIPMENT_REGISTERED = "equipment.registered"
    EQUIPMENT_UPDATED = "equipment.updated"
    
    # Ticket Events
    TICKET_CREATED = "ticket.created"
    TICKET_UPDATED = "ticket.updated"
    TICKET_RESOLVED = "ticket.resolved"

class EventPriority(Enum):
    """Priorytety zdarzeń"""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class Event:
    """Zdarzenie w systemie"""
    id: str
    type: EventType
    source: str
    target: Optional[str]
    data: Dict[str, Any]
    priority: EventPriority
    timestamp: datetime
    correlation_id: Optional[str] = None
    retry_count: int = 0
    max_retries: int = 3
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if not self.timestamp:
            self.timestamp = datetime.now()

@dataclass
class EventSubscription:
    """Subskrypcja zdarzenia"""
    id: str
    event_types: Set[EventType]
    callback: Callable
    filters: Dict[str, Any]
    active: bool = True

class EventBus:
    """🚌 Magistrala zdarzeń"""
    
    def __init__(self):
        self.subscribers: Dict[str, EventSubscription] = {}
        self.event_history: List[Event] = []
        self.max_history = 1000
        
        # Kolejki priorytetowe
        self.priority_queues = {
            priority: asyncio.Queue() for priority in EventPriority
        }
        
        # Statystyki
        self.stats = {
            "events_published": 0,
            "events_processed": 0,
            "events_failed": 0,
            "subscribers_count": 0
        }
        
        # Worker tasks
        self.workers = []
        self.running = False
    
    async def start(self):
        """🚀 Uruchom magistralę zdarzeń"""
        if self.running:
            return
        
        self.running = True
        
        # Uruchom workery dla każdego priorytetu
        for priority in EventPriority:
            worker = asyncio.create_task(self._event_worker(priority))
            self.workers.append(worker)
        
        logger.info("🚌 Event Bus uruchomiony")
    
    async def stop(self):
        """🛑 Zatrzymaj magistralę zdarzeń"""
        self.running = False
        
        # Zatrzymaj wszystkich workerów
        for worker in self.workers:
            worker.cancel()
        
        await asyncio.gather(*self.workers, return_exceptions=True)
        self.workers.clear()
        
        logger.info("🛑 Event Bus zatrzymany")
    
    async def publish(self, event: Event):
        """📢 Opublikuj zdarzenie"""
        try:
            # Dodaj do historii
            self.event_history.append(event)
            if len(self.event_history) > self.max_history:
                self.event_history.pop(0)
            
            # Dodaj do odpowiedniej kolejki priorytetowej
            await self.priority_queues[event.priority].put(event)
            
            self.stats["events_published"] += 1
            
            logger.debug(f"📢 Opublikowano zdarzenie {event.type.value} (ID: {event.id})")
            
        except Exception as e:
            logger.error(f"❌ Błąd publikowania zdarzenia: {e}")
    
    async def _event_worker(self, priority: EventPriority):
        """👷 Worker przetwarzający zdarzenia o danym priorytecie"""
        queue = self.priority_queues[priority]
        
        while self.running:
            try:
                # Pobierz zdarzenie z kolejki
                event = await queue.get()
                
                # Przetwórz zdarzenie
                await self._process_event(event)
                
                self.stats["events_processed"] += 1
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Błąd w event worker ({priority.name}): {e}")
                self.stats["events_failed"] += 1
                await asyncio.sleep(1)
    
    async def _process_event(self, event: Event):
        """🔄 Przetwórz zdarzenie"""
        # Znajdź pasujących subskrybentów
        matching_subscribers = []
        
        for subscription in self.subscribers.values():
            if not subscription.active:
                continue
            
            # Sprawdź typ zdarzenia
            if event.type not in subscription.event_types:
                continue
            
            # Sprawdź filtry
            if not self._check_filters(event, subscription.filters):
                continue
            
            matching_subscribers.append(subscription)
        
        # Wywołaj callbacki subskrybentów
        if matching_subscribers:
            tasks = []
            for subscription in matching_subscribers:
                task = asyncio.create_task(
                    self._call_subscriber(subscription, event)
                )
                tasks.append(task)
            
            # Poczekaj na wszystkie callbacki
            await asyncio.gather(*tasks, return_exceptions=True)
        
        logger.debug(f"🔄 Przetworzono zdarzenie {event.type.value} dla {len(matching_subscribers)} subskrybentów")
    
    def _check_filters(self, event: Event, filters: Dict[str, Any]) -> bool:
        """🔍 Sprawdź czy zdarzenie pasuje do filtrów"""
        for key, value in filters.items():
            if key == "source" and event.source != value:
                return False
            elif key == "target" and event.target != value:
                return False
            elif key in event.data and event.data[key] != value:
                return False
        
        return True
    
    async def _call_subscriber(self, subscription: EventSubscription, event: Event):
        """📞 Wywołaj callback subskrybenta"""
        try:
            if asyncio.iscoroutinefunction(subscription.callback):
                await subscription.callback(event)
            else:
                subscription.callback(event)
        
        except Exception as e:
            logger.error(f"❌ Błąd w callback subskrybenta {subscription.id}: {e}")
    
    def subscribe(
        self, 
        event_types: List[EventType], 
        callback: Callable,
        filters: Optional[Dict[str, Any]] = None
    ) -> str:
        """📝 Subskrybuj zdarzenia"""
        subscription_id = str(uuid.uuid4())
        
        subscription = EventSubscription(
            id=subscription_id,
            event_types=set(event_types),
            callback=callback,
            filters=filters or {}
        )
        
        self.subscribers[subscription_id] = subscription
        self.stats["subscribers_count"] = len(self.subscribers)
        
        logger.info(f"📝 Nowa subskrypcja {subscription_id} dla {[et.value for et in event_types]}")
        
        return subscription_id
    
    def unsubscribe(self, subscription_id: str):
        """❌ Anuluj subskrypcję"""
        if subscription_id in self.subscribers:
            del self.subscribers[subscription_id]
            self.stats["subscribers_count"] = len(self.subscribers)
            logger.info(f"❌ Anulowano subskrypcję {subscription_id}")
    
    def get_stats(self) -> Dict[str, Any]:
        """📊 Pobierz statystyki"""
        return {
            **self.stats,
            "queue_sizes": {
                priority.name: queue.qsize() 
                for priority, queue in self.priority_queues.items()
            },
            "history_size": len(self.event_history)
        }

class RedisEventBus(EventBus):
    """🚌 Magistrala zdarzeń z Redis backend"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379"):
        super().__init__()
        self.redis_url = redis_url
        self.redis = None
        self.pubsub = None
    
    async def start(self):
        """🚀 Uruchom z Redis"""
        try:
            self.redis = await aioredis.from_url(self.redis_url)
            self.pubsub = self.redis.pubsub()
            
            # Subskrybuj kanały Redis
            await self.pubsub.subscribe("hvac_events")
            
            # Uruchom listener Redis
            asyncio.create_task(self._redis_listener())
            
            await super().start()
            
            logger.info("🚌 Redis Event Bus uruchomiony")
            
        except Exception as e:
            logger.warning(f"⚠️ Nie można połączyć z Redis, używam lokalnego Event Bus: {e}")
            await super().start()
    
    async def _redis_listener(self):
        """👂 Nasłuchuj zdarzeń z Redis"""
        try:
            async for message in self.pubsub.listen():
                if message["type"] == "message":
                    try:
                        event_data = json.loads(message["data"])
                        event = Event(**event_data)
                        await super().publish(event)
                    except Exception as e:
                        logger.error(f"❌ Błąd przetwarzania zdarzenia z Redis: {e}")
        
        except Exception as e:
            logger.error(f"❌ Błąd Redis listener: {e}")
    
    async def publish(self, event: Event):
        """📢 Opublikuj zdarzenie (lokalnie i Redis)"""
        # Publikuj lokalnie
        await super().publish(event)
        
        # Publikuj do Redis
        if self.redis:
            try:
                event_data = asdict(event)
                event_data["timestamp"] = event.timestamp.isoformat()
                event_data["type"] = event.type.value
                event_data["priority"] = event.priority.value
                
                await self.redis.publish("hvac_events", json.dumps(event_data))
                
            except Exception as e:
                logger.error(f"❌ Błąd publikowania do Redis: {e}")

class RealTimeEventSystem:
    """⚡ System zdarzeń w czasie rzeczywistym"""
    
    def __init__(self, use_redis: bool = True):
        if use_redis:
            self.event_bus = RedisEventBus()
        else:
            self.event_bus = EventBus()
        
        # WebSocket server dla klientów
        self.ws_clients: Set[websockets.WebSocketServerProtocol] = set()
        self.ws_server = None
        
        # Event handlers
        self.handlers = {}
        
    async def initialize(self):
        """🚀 Inicjalizacja systemu zdarzeń"""
        logger.info("⚡ Inicjalizacja Real-Time Event System...")
        
        # Uruchom Event Bus
        await self.event_bus.start()
        
        # Uruchom WebSocket server
        await self._start_websocket_server()
        
        # Zarejestruj podstawowe handlery
        self._register_default_handlers()
        
        logger.info("✅ Real-Time Event System zainicjalizowany")
    
    async def _start_websocket_server(self):
        """🔌 Uruchom serwer WebSocket"""
        async def handle_client(websocket, path):
            self.ws_clients.add(websocket)
            logger.info(f"🔌 Nowy klient WebSocket: {websocket.remote_address}")
            
            try:
                # Wyślij aktualny status
                await self._send_status_to_client(websocket)
                
                # Nasłuchuj wiadomości od klienta
                async for message in websocket:
                    try:
                        data = json.loads(message)
                        await self._handle_client_message(websocket, data)
                    except Exception as e:
                        logger.error(f"❌ Błąd przetwarzania wiadomości klienta: {e}")
            
            except websockets.exceptions.ConnectionClosed:
                pass
            except Exception as e:
                logger.error(f"❌ Błąd WebSocket: {e}")
            finally:
                self.ws_clients.discard(websocket)
                logger.info(f"🔌 Klient WebSocket rozłączony: {websocket.remote_address}")
        
        try:
            self.ws_server = await websockets.serve(handle_client, "localhost", 9002)
            logger.info("🔌 WebSocket server uruchomiony na porcie 9002")
        except Exception as e:
            logger.error(f"❌ Błąd uruchamiania WebSocket server: {e}")
    
    async def _send_status_to_client(self, websocket):
        """📊 Wyślij status do klienta"""
        status = {
            "type": "status",
            "event_bus_stats": self.event_bus.get_stats(),
            "connected_clients": len(self.ws_clients),
            "timestamp": datetime.now().isoformat()
        }
        
        await websocket.send(json.dumps(status))
    
    async def _handle_client_message(self, websocket, data: Dict[str, Any]):
        """📨 Obsłuż wiadomość od klienta"""
        message_type = data.get("type")
        
        if message_type == "subscribe":
            # Klient chce subskrybować zdarzenia
            event_types = [EventType(et) for et in data.get("event_types", [])]
            
            def client_callback(event: Event):
                asyncio.create_task(self._send_event_to_client(websocket, event))
            
            subscription_id = self.event_bus.subscribe(event_types, client_callback)
            
            await websocket.send(json.dumps({
                "type": "subscription_confirmed",
                "subscription_id": subscription_id
            }))
        
        elif message_type == "get_stats":
            await self._send_status_to_client(websocket)
    
    async def _send_event_to_client(self, websocket, event: Event):
        """📤 Wyślij zdarzenie do klienta WebSocket"""
        try:
            event_data = {
                "type": "event",
                "event": {
                    "id": event.id,
                    "type": event.type.value,
                    "source": event.source,
                    "target": event.target,
                    "data": event.data,
                    "priority": event.priority.value,
                    "timestamp": event.timestamp.isoformat(),
                    "correlation_id": event.correlation_id
                }
            }
            
            await websocket.send(json.dumps(event_data))
            
        except Exception as e:
            logger.error(f"❌ Błąd wysyłania zdarzenia do klienta: {e}")
    
    def _register_default_handlers(self):
        """📝 Zarejestruj domyślne handlery zdarzeń"""
        
        # Handler dla zdarzeń transkrypcji
        async def transcription_handler(event: Event):
            if event.type == EventType.TRANSCRIPTION_COMPLETED:
                logger.info(f"✅ Transkrypcja ukończona: {event.data.get('email_id')}")
                
                # Broadcast do wszystkich klientów WebSocket
                await self._broadcast_to_clients(event)
        
        # Handler dla zdarzeń synchronizacji
        async def sync_handler(event: Event):
            if event.type in [EventType.SYNC_COMPLETED, EventType.SYNC_FAILED]:
                logger.info(f"🔄 Synchronizacja: {event.type.value}")
                await self._broadcast_to_clients(event)
        
        # Zarejestruj handlery
        self.event_bus.subscribe(
            [EventType.TRANSCRIPTION_COMPLETED, EventType.TRANSCRIPTION_FAILED],
            transcription_handler
        )
        
        self.event_bus.subscribe(
            [EventType.SYNC_STARTED, EventType.SYNC_COMPLETED, EventType.SYNC_FAILED],
            sync_handler
        )
    
    async def _broadcast_to_clients(self, event: Event):
        """📡 Rozgłoś zdarzenie do wszystkich klientów"""
        if self.ws_clients:
            tasks = [
                self._send_event_to_client(client, event)
                for client in self.ws_clients.copy()
            ]
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def emit(
        self, 
        event_type: EventType, 
        source: str,
        data: Dict[str, Any],
        target: Optional[str] = None,
        priority: EventPriority = EventPriority.NORMAL,
        correlation_id: Optional[str] = None
    ):
        """⚡ Wyemituj zdarzenie"""
        event = Event(
            id=str(uuid.uuid4()),
            type=event_type,
            source=source,
            target=target,
            data=data,
            priority=priority,
            timestamp=datetime.now(),
            correlation_id=correlation_id
        )
        
        await self.event_bus.publish(event)
    
    def subscribe(
        self, 
        event_types: List[EventType], 
        callback: Callable,
        filters: Optional[Dict[str, Any]] = None
    ) -> str:
        """📝 Subskrybuj zdarzenia"""
        return self.event_bus.subscribe(event_types, callback, filters)
    
    def unsubscribe(self, subscription_id: str):
        """❌ Anuluj subskrypcję"""
        self.event_bus.unsubscribe(subscription_id)
    
    async def shutdown(self):
        """🛑 Zamknij system zdarzeń"""
        logger.info("🛑 Zamykanie Real-Time Event System...")
        
        # Zamknij WebSocket server
        if self.ws_server:
            self.ws_server.close()
            await self.ws_server.wait_closed()
        
        # Zamknij Event Bus
        await self.event_bus.stop()
        
        logger.info("✅ Real-Time Event System zamknięty")

# Globalna instancja systemu zdarzeń
event_system = RealTimeEventSystem()
