#!/usr/bin/env python3
"""
Enhanced MCP Manager for HVAC Python Mixer
Coordinates Memory and Tavily MCP clients with Go Backend integration
Implements technological advancements from the comprehensive system improvement plan
"""

import asyncio
import time
import aiohttp
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

from loguru import logger

from .memory_client import MemoryClient, ProjectState, DevelopmentSession
from .tavily_client import TavilyClient, HVACEquipmentSpec, ResearchResult


@dataclass
class MCPStatus:
    """Enhanced MCP system status with Go backend integration."""
    memory_connected: bool
    tavily_connected: bool
    go_backend_connected: bool
    nats_connected: bool
    last_sync: Optional[datetime]
    total_operations: int
    error_count: int
    performance_score: float
    circuit_breaker_status: Dict[str, str]
    prometheus_metrics: Dict[str, Any]

@dataclass
class GoBackendConfig:
    """Configuration for Go backend integration."""
    base_url: str = "http://localhost:8080"
    api_key: Optional[str] = None
    timeout: int = 30
    retry_attempts: int = 3
    enable_circuit_breaker: bool = True

@dataclass
class TranscriptionJobRequest:
    """Request for transcription job via Go backend."""
    job_id: str
    email_source: str
    file_name: str
    audio_path: str
    priority: int = 1
    metadata: Dict[str, Any] = None

@dataclass
class SystemHealthMetrics:
    """System health metrics from Go backend."""
    overall_status: str
    services: Dict[str, Dict[str, Any]]
    uptime: float
    active_connections: int
    last_updated: datetime


class MCPManager:
    """
    MCP Manager for coordinating Memory and Tavily clients.
    
    Provides unified interface for:
    - Project state management and persistence
    - Real-time HVAC research capabilities
    - Development progress tracking
    - Performance monitoring and optimization
    """
    
    def __init__(
        self,
        memory_server_url: str = "http://localhost:3001",
        tavily_server_url: str = "http://localhost:3002",
        go_backend_config: Optional[GoBackendConfig] = None
    ):
        self.memory_client = MemoryClient(memory_server_url)
        self.tavily_client = TavilyClient(tavily_server_url)
        self.go_backend_config = go_backend_config or GoBackendConfig()

        # HTTP session for Go backend communication
        self.session: Optional[aiohttp.ClientSession] = None

        # Enhanced system status tracking
        self.status = MCPStatus(
            memory_connected=False,
            tavily_connected=False,
            go_backend_connected=False,
            nats_connected=False,
            last_sync=None,
            total_operations=0,
            error_count=0,
            performance_score=0.0,
            circuit_breaker_status={},
            prometheus_metrics={}
        )
        
        # Operation tracking
        self.operation_history = []
        self.performance_metrics = {
            "memory_operations": [],
            "research_operations": [],
            "sync_times": [],
            "error_rates": []
        }
        
    async def initialize(self) -> bool:
        """Initialize MCP clients and Go backend integration."""
        logger.info("🚀 Initializing Enhanced MCP Manager for HVAC Python Mixer")

        try:
            # Initialize HTTP session for Go backend
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.go_backend_config.timeout)
            )

            # Initialize Memory client
            memory_success = await self.memory_client.initialize()
            self.status.memory_connected = memory_success

            if memory_success:
                logger.success("✅ Memory MCP client initialized")
            else:
                logger.warning("⚠️ Memory MCP client initialization failed")

            # Initialize Tavily client
            tavily_success = await self.tavily_client.initialize()
            self.status.tavily_connected = tavily_success

            if tavily_success:
                logger.success("✅ Tavily research client initialized")
            else:
                logger.warning("⚠️ Tavily research client initialization failed")

            # Initialize Go backend connection
            go_backend_success = await self.initialize_go_backend()
            self.status.go_backend_connected = go_backend_success

            if go_backend_success:
                logger.success("✅ Go backend integration initialized")
                # Check NATS connection through Go backend
                await self.check_nats_status()
            else:
                logger.warning("⚠️ Go backend integration failed")

            # Update status
            self.status.last_sync = datetime.now()

            # Calculate performance score
            connections = [memory_success, tavily_success, go_backend_success]
            self.status.performance_score = sum(connections) / len(connections)

            # Log initialization summary
            if all(connections):
                logger.success("🎉 Enhanced MCP Manager fully operational!")
            elif any(connections):
                logger.info("⚡ Enhanced MCP Manager partially operational")
            else:
                logger.warning("⚠️ Enhanced MCP Manager in fallback mode")

            # Start development session tracking
            if memory_success:
                await self.start_development_tracking()

            return any(connections)

        except Exception as e:
            logger.error(f"Enhanced MCP Manager initialization failed: {e}")
            self.status.error_count += 1
            return False
    
    async def start_development_tracking(self) -> str:
        """Start development session tracking."""
        try:
            session_id = await self.memory_client.start_development_session()
            
            # Log initial project state
            await self.save_project_state({
                "phase": "Phase 1: Foundation Enhancement",
                "components_status": {
                    "uv_setup": "in_progress",
                    "mcp_memory": "completed",
                    "mcp_tavily": "completed",
                    "csv_processing": "pending",
                    "lm_studio": "pending",
                    "orchestrator": "pending",
                    "calendar_testing": "pending"
                },
                "performance_metrics": {
                    "mcp_initialization_time": time.time(),
                    "memory_connected": self.status.memory_connected,
                    "tavily_connected": self.status.tavily_connected
                },
                "development_progress": {
                    "total_tasks": 7,
                    "completed_tasks": 2,
                    "current_phase": "Foundation Enhancement"
                },
                "user_context": {
                    "project_type": "HVAC CRM Enhancement",
                    "methodology": "Systematic MCP-based development",
                    "target_completion": "5 days"
                },
                "errors": [],
                "achievements": [
                    "MCP Memory Integration completed",
                    "Tavily Research Integration completed"
                ]
            })
            
            logger.info(f"📊 Development tracking started: {session_id}")
            return session_id
            
        except Exception as e:
            logger.error(f"Failed to start development tracking: {e}")
            return "fallback_session"
    
    async def update_task_progress(self, task: str, status: str, notes: str = "") -> bool:
        """Update task progress with enhanced tracking."""
        try:
            # Update in memory client
            success = await self.memory_client.update_task_progress(task, status, notes)
            
            # Log operation
            self.operation_history.append({
                "type": "task_update",
                "task": task,
                "status": status,
                "timestamp": datetime.now(),
                "success": success
            })
            
            self.status.total_operations += 1
            if not success:
                self.status.error_count += 1
            
            # Update performance score
            self._update_performance_score()
            
            logger.info(f"📝 Task updated: {task} -> {status}")
            return success
            
        except Exception as e:
            logger.error(f"Task progress update failed: {e}")
            self.status.error_count += 1
            return False
    
    async def research_hvac_equipment(
        self, 
        model: str, 
        manufacturer: Optional[str] = None
    ) -> Optional[HVACEquipmentSpec]:
        """Research HVAC equipment with memory tracking."""
        start_time = time.time()
        
        try:
            logger.info(f"🔍 Researching equipment: {model}")
            
            # Perform research
            equipment_spec = await self.tavily_client.research_hvac_equipment(
                model, manufacturer
            )
            
            research_time = time.time() - start_time
            
            # Track performance
            self.performance_metrics["research_operations"].append(research_time)
            self.status.total_operations += 1
            
            # Save research result to memory
            if equipment_spec and self.status.memory_connected:
                await self.save_research_result({
                    "type": "equipment_research",
                    "model": model,
                    "manufacturer": manufacturer,
                    "result": equipment_spec.__dict__ if equipment_spec else None,
                    "research_time": research_time,
                    "timestamp": datetime.now().isoformat()
                })
            
            logger.success(f"✅ Equipment research completed in {research_time:.2f}s")
            return equipment_spec
            
        except Exception as e:
            logger.error(f"Equipment research failed: {e}")
            self.status.error_count += 1
            return None
    
    async def research_industry_trends(self, topic: str) -> List[str]:
        """Research industry trends with memory tracking."""
        try:
            logger.info(f"📈 Researching industry trends: {topic}")
            
            trends = await self.tavily_client.research_industry_trends(topic)
            
            # Save to memory
            if trends and self.status.memory_connected:
                await self.save_research_result({
                    "type": "industry_trends",
                    "topic": topic,
                    "trends": trends,
                    "timestamp": datetime.now().isoformat()
                })
            
            self.status.total_operations += 1
            logger.success(f"✅ Found {len(trends)} industry insights")
            
            return trends
            
        except Exception as e:
            logger.error(f"Industry trends research failed: {e}")
            self.status.error_count += 1
            return []
    
    async def save_project_state(self, state_data: Dict[str, Any]) -> bool:
        """Save project state with enhanced tracking."""
        try:
            if not self.status.memory_connected:
                logger.warning("Memory client not connected, skipping state save")
                return False
            
            success = await self.memory_client.save_project_state(state_data)
            
            self.status.total_operations += 1
            if success:
                self.status.last_sync = datetime.now()
                logger.debug("📊 Project state saved successfully")
            else:
                self.status.error_count += 1
                logger.warning("Failed to save project state")
            
            return success
            
        except Exception as e:
            logger.error(f"Project state save failed: {e}")
            self.status.error_count += 1
            return False
    
    async def save_research_result(self, research_data: Dict[str, Any]) -> bool:
        """Save research result to memory."""
        try:
            if not self.status.memory_connected:
                return False
            
            # Extend project state with research data
            current_state = await self.memory_client.load_project_state()
            if current_state:
                # Add research data to achievements
                research_summary = f"Research completed: {research_data.get('type', 'unknown')}"
                if research_summary not in current_state.achievements:
                    current_state.achievements.append(research_summary)
                
                # Update state
                await self.memory_client.save_project_state(current_state.__dict__)
            
            return True
            
        except Exception as e:
            logger.error(f"Research result save failed: {e}")
            return False
    
    async def get_comprehensive_status(self) -> Dict[str, Any]:
        """Get comprehensive MCP system status."""
        try:
            # Get memory client summary
            memory_summary = {}
            if self.status.memory_connected:
                memory_summary = await self.memory_client.get_development_summary()
            
            # Get Tavily statistics
            tavily_stats = {}
            if self.status.tavily_connected:
                tavily_stats = await self.tavily_client.get_research_statistics()
            
            # Calculate performance metrics
            avg_research_time = 0.0
            if self.performance_metrics["research_operations"]:
                avg_research_time = sum(self.performance_metrics["research_operations"]) / len(self.performance_metrics["research_operations"])
            
            error_rate = 0.0
            if self.status.total_operations > 0:
                error_rate = (self.status.error_count / self.status.total_operations) * 100
            
            return {
                "mcp_status": {
                    "memory_connected": self.status.memory_connected,
                    "tavily_connected": self.status.tavily_connected,
                    "last_sync": self.status.last_sync.isoformat() if self.status.last_sync else None,
                    "performance_score": self.status.performance_score,
                    "total_operations": self.status.total_operations,
                    "error_count": self.status.error_count,
                    "error_rate": f"{error_rate:.1f}%"
                },
                "memory_client": memory_summary,
                "tavily_client": tavily_stats,
                "performance_metrics": {
                    "average_research_time": f"{avg_research_time:.2f}s",
                    "total_research_operations": len(self.performance_metrics["research_operations"]),
                    "system_uptime": str(datetime.now() - self.status.last_sync) if self.status.last_sync else "Unknown"
                },
                "recent_operations": self.operation_history[-5:] if self.operation_history else []
            }
            
        except Exception as e:
            logger.error(f"Status retrieval failed: {e}")
            return {"error": str(e)}
    
    def _update_performance_score(self):
        """Update system performance score."""
        try:
            base_score = 0.0
            
            # Connection scores
            if self.status.memory_connected:
                base_score += 0.5
            if self.status.tavily_connected:
                base_score += 0.3
            
            # Error rate penalty
            if self.status.total_operations > 0:
                error_rate = self.status.error_count / self.status.total_operations
                base_score *= (1.0 - error_rate)
            
            # Performance bonus
            if self.performance_metrics["research_operations"]:
                avg_time = sum(self.performance_metrics["research_operations"]) / len(self.performance_metrics["research_operations"])
                if avg_time < 5.0:  # Fast research
                    base_score += 0.2
            
            self.status.performance_score = min(1.0, base_score)
            
        except Exception as e:
            logger.error(f"Performance score update failed: {e}")
    
    async def initialize_go_backend(self) -> bool:
        """Initialize connection to Go backend services."""
        try:
            if not self.session:
                return False

            # Test connection to Go backend
            url = f"{self.go_backend_config.base_url}/api/health"
            headers = {}
            if self.go_backend_config.api_key:
                headers["Authorization"] = f"Bearer {self.go_backend_config.api_key}"

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    health_data = await response.json()
                    logger.info(f"🔗 Go backend health: {health_data}")
                    return True
                else:
                    logger.warning(f"Go backend health check failed: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"Go backend initialization failed: {e}")
            return False

    async def check_nats_status(self) -> bool:
        """Check NATS connection status through Go backend."""
        try:
            if not self.session or not self.status.go_backend_connected:
                return False

            url = f"{self.go_backend_config.base_url}/api/nats/health"
            headers = {}
            if self.go_backend_config.api_key:
                headers["Authorization"] = f"Bearer {self.go_backend_config.api_key}"

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    nats_data = await response.json()
                    self.status.nats_connected = nats_data.get("connected", False)
                    logger.info(f"📡 NATS status: {nats_data}")
                    return self.status.nats_connected
                else:
                    self.status.nats_connected = False
                    return False

        except Exception as e:
            logger.error(f"NATS status check failed: {e}")
            self.status.nats_connected = False
            return False

    async def submit_transcription_job(self, job_request: TranscriptionJobRequest) -> bool:
        """Submit transcription job to Go backend via NATS."""
        try:
            if not self.session or not self.status.go_backend_connected:
                logger.warning("Go backend not connected, cannot submit transcription job")
                return False

            url = f"{self.go_backend_config.base_url}/api/transcription/submit"
            headers = {"Content-Type": "application/json"}
            if self.go_backend_config.api_key:
                headers["Authorization"] = f"Bearer {self.go_backend_config.api_key}"

            job_data = asdict(job_request)
            if job_data["metadata"] is None:
                job_data["metadata"] = {}

            async with self.session.post(url, headers=headers, json=job_data) as response:
                if response.status == 200:
                    result = await response.json()
                    logger.success(f"✅ Transcription job submitted: {job_request.job_id}")

                    # Track in memory if available
                    if self.status.memory_connected:
                        await self.save_project_state({
                            "transcription_jobs": {
                                job_request.job_id: {
                                    "status": "submitted",
                                    "timestamp": datetime.now().isoformat(),
                                    "source": job_request.email_source,
                                    "file": job_request.file_name
                                }
                            }
                        })

                    return True
                else:
                    logger.error(f"Transcription job submission failed: {response.status}")
                    return False

        except Exception as e:
            logger.error(f"Transcription job submission failed: {e}")
            return False

    async def get_system_health(self) -> Optional[SystemHealthMetrics]:
        """Get comprehensive system health from Go backend."""
        try:
            if not self.session or not self.status.go_backend_connected:
                return None

            url = f"{self.go_backend_config.base_url}/api/system/health"
            headers = {}
            if self.go_backend_config.api_key:
                headers["Authorization"] = f"Bearer {self.go_backend_config.api_key}"

            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    health_data = await response.json()

                    # Update circuit breaker status
                    if "circuit_breakers" in health_data:
                        self.status.circuit_breaker_status = health_data["circuit_breakers"]

                    # Update Prometheus metrics
                    if "prometheus_metrics" in health_data:
                        self.status.prometheus_metrics = health_data["prometheus_metrics"]

                    return SystemHealthMetrics(
                        overall_status=health_data.get("overall", "unknown"),
                        services=health_data.get("services", {}),
                        uptime=health_data.get("uptime", 0.0),
                        active_connections=health_data.get("active_connections", 0),
                        last_updated=datetime.now()
                    )
                else:
                    logger.warning(f"System health check failed: {response.status}")
                    return None

        except Exception as e:
            logger.error(f"System health check failed: {e}")
            return None

    async def close(self):
        """Close MCP Manager and all clients."""
        logger.info("🔄 Closing Enhanced MCP Manager...")

        try:
            # Close HTTP session
            if self.session:
                await self.session.close()

            # Close MCP clients
            await self.memory_client.close()
            await self.tavily_client.close()

            logger.success("✅ Enhanced MCP Manager closed successfully")

        except Exception as e:
            logger.error(f"Enhanced MCP Manager close failed: {e}")


# Convenience function for easy initialization
async def create_mcp_manager(
    memory_url: str = "http://localhost:3001",
    tavily_url: str = "http://localhost:3002"
) -> MCPManager:
    """Create and initialize MCP Manager."""
    manager = MCPManager(memory_url, tavily_url)
    await manager.initialize()
    return manager
