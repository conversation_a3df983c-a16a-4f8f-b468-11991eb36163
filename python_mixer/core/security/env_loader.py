#!/usr/bin/env python3
"""
Secure Environment Variable Loader for python_mixer
Handles loading and validation of environment variables with security best practices
"""

import os
import sys
from pathlib import Path
from typing import Dict, List, Optional, Any
from loguru import logger

try:
    from dotenv import load_dotenv
except ImportError:
    logger.warning("python-dotenv not installed. Install with: pip install python-dotenv")
    load_dotenv = None


class SecureEnvLoader:
    """Secure environment variable loader with validation and fallbacks."""
    
    def __init__(self, env_file: Optional[Path] = None):
        self.env_file = env_file or Path(__file__).parent.parent.parent / ".env"
        self.required_vars: Dict[str, str] = {}
        self.optional_vars: Dict[str, Any] = {}
        self.loaded = False
        
    def load_environment(self) -> bool:
        """Load environment variables from .env file if available."""
        try:
            if load_dotenv and self.env_file.exists():
                load_dotenv(self.env_file)
                logger.info(f"✅ Environment variables loaded from {self.env_file}")
                self.loaded = True
                return True
            elif self.env_file.exists():
                logger.warning("⚠️ .env file found but python-dotenv not installed")
                logger.info("Install with: pip install python-dotenv")
            else:
                logger.info("No .env file found, using system environment variables")
                
            self.loaded = True
            return True
            
        except Exception as e:
            logger.error(f"Failed to load environment variables: {e}")
            return False
    
    def register_required(self, var_name: str, description: str = ""):
        """Register a required environment variable."""
        self.required_vars[var_name] = description
    
    def register_optional(self, var_name: str, default_value: Any = None, description: str = ""):
        """Register an optional environment variable with default."""
        self.optional_vars[var_name] = {
            "default": default_value,
            "description": description
        }
    
    def validate_required_vars(self) -> bool:
        """Validate that all required environment variables are set."""
        if not self.loaded:
            self.load_environment()
            
        missing_vars = []
        for var_name, description in self.required_vars.items():
            if not os.getenv(var_name):
                missing_vars.append(f"{var_name} - {description}")
        
        if missing_vars:
            logger.error("🚨 CRITICAL: Missing required environment variables:")
            for var in missing_vars:
                logger.error(f"  - {var}")
            logger.error("\nPlease set these environment variables or create a .env file")
            logger.error(f"Template available at: {self.env_file.parent}/.env.template")
            return False
        
        logger.success("✅ All required environment variables are set")
        return True
    
    def get_var(self, var_name: str, default: Any = None, required: bool = False) -> Any:
        """Get environment variable with optional default and validation."""
        if not self.loaded:
            self.load_environment()
            
        value = os.getenv(var_name, default)
        
        if required and value is None:
            logger.error(f"Required environment variable '{var_name}' is not set")
            raise ValueError(f"Required environment variable '{var_name}' is not set")
        
        return value
    
    def get_bool(self, var_name: str, default: bool = False) -> bool:
        """Get boolean environment variable."""
        value = self.get_var(var_name, str(default))
        return value.lower() in ('true', '1', 'yes', 'on')
    
    def get_int(self, var_name: str, default: int = 0) -> int:
        """Get integer environment variable."""
        value = self.get_var(var_name, str(default))
        try:
            return int(value)
        except ValueError:
            logger.warning(f"Invalid integer value for {var_name}: {value}, using default: {default}")
            return default
    
    def get_float(self, var_name: str, default: float = 0.0) -> float:
        """Get float environment variable."""
        value = self.get_var(var_name, str(default))
        try:
            return float(value)
        except ValueError:
            logger.warning(f"Invalid float value for {var_name}: {value}, using default: {default}")
            return default
    
    def get_list(self, var_name: str, separator: str = ",", default: List[str] = None) -> List[str]:
        """Get list environment variable (comma-separated by default)."""
        if default is None:
            default = []
        value = self.get_var(var_name, separator.join(default))
        return [item.strip() for item in value.split(separator) if item.strip()]
    
    def mask_sensitive_value(self, value: str, show_chars: int = 4) -> str:
        """Mask sensitive values for logging."""
        if not value or len(value) <= show_chars:
            return "*" * len(value) if value else ""
        return value[:show_chars] + "*" * (len(value) - show_chars)
    
    def get_environment_summary(self) -> Dict[str, Any]:
        """Get summary of environment configuration (with masked sensitive values)."""
        if not self.loaded:
            self.load_environment()
            
        summary = {
            "loaded_from_file": self.env_file.exists(),
            "env_file_path": str(self.env_file),
            "required_vars_status": {},
            "optional_vars_status": {},
            "system_info": {
                "python_version": sys.version,
                "platform": sys.platform,
                "cwd": os.getcwd()
            }
        }
        
        # Check required variables
        for var_name, description in self.required_vars.items():
            value = os.getenv(var_name)
            summary["required_vars_status"][var_name] = {
                "set": value is not None,
                "description": description,
                "masked_value": self.mask_sensitive_value(value) if value else None
            }
        
        # Check optional variables
        for var_name, config in self.optional_vars.items():
            value = os.getenv(var_name, config["default"])
            summary["optional_vars_status"][var_name] = {
                "set": os.getenv(var_name) is not None,
                "using_default": os.getenv(var_name) is None,
                "description": config["description"],
                "masked_value": self.mask_sensitive_value(str(value)) if value else None
            }
        
        return summary


# Global instance for python_mixer
env_loader = SecureEnvLoader()

# Register required variables for python_mixer
env_loader.register_required("DOLORES_EMAIL_PASSWORD", "<NAME_EMAIL> email account")
env_loader.register_required("GRZEGORZ_EMAIL_PASSWORD", "<NAME_EMAIL> email account")
env_loader.register_required("POSTGRES_USER", "PostgreSQL database username")
env_loader.register_required("POSTGRES_PASSWORD", "PostgreSQL database password")
env_loader.register_required("MINIO_ACCESS_KEY", "MinIO object storage access key")
env_loader.register_required("MINIO_SECRET_KEY", "MinIO object storage secret key")

# Register optional variables with defaults
env_loader.register_optional("POSTGRES_HOST", "**************", "PostgreSQL host address")
env_loader.register_optional("POSTGRES_PORT", 5432, "PostgreSQL port")
env_loader.register_optional("POSTGRES_DB", "hvac_crm", "PostgreSQL database name")
env_loader.register_optional("MINIO_ENDPOINT", "**************:9000", "MinIO endpoint")
env_loader.register_optional("REDIS_URL", "redis://localhost:6379", "Redis connection URL")
env_loader.register_optional("LOG_LEVEL", "INFO", "Logging level")
env_loader.register_optional("PROMETHEUS_ENABLED", True, "Enable Prometheus metrics")


def validate_environment() -> bool:
    """Validate environment variables for python_mixer."""
    return env_loader.validate_required_vars()


def get_env_summary() -> Dict[str, Any]:
    """Get environment summary for debugging."""
    return env_loader.get_environment_summary()


if __name__ == "__main__":
    # Test the environment loader
    logger.info("🧪 Testing Secure Environment Loader")
    
    # Load environment
    env_loader.load_environment()
    
    # Validate required variables
    if env_loader.validate_required_vars():
        logger.success("✅ Environment validation passed")
    else:
        logger.error("❌ Environment validation failed")
        sys.exit(1)
    
    # Show summary
    summary = env_loader.get_environment_summary()
    logger.info("Environment Summary:")
    logger.info(f"  Loaded from file: {summary['loaded_from_file']}")
    logger.info(f"  Required vars set: {sum(1 for v in summary['required_vars_status'].values() if v['set'])}/{len(summary['required_vars_status'])}")
    logger.info(f"  Optional vars using defaults: {sum(1 for v in summary['optional_vars_status'].values() if v['using_default'])}")
