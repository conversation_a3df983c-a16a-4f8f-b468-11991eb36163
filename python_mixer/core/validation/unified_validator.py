#!/usr/bin/env python3
"""
Unified Data Validation Service for python_mixer
Consolidates all validation logic into a single, comprehensive service
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional, Union, Type
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel, ValidationError
from loguru import logger

from ..data_processing.data_validator import DataValidator, ValidationResult
from ..queues import queue_manager, QueuePriority


class ValidationLevel(Enum):
    """Validation strictness levels."""
    STRICT = "strict"      # All validations must pass
    STANDARD = "standard"  # Core validations must pass, warnings allowed
    LENIENT = "lenient"    # Basic validations only


class DataType(Enum):
    """Supported data types for validation."""
    SERVICE_ORDER = "service_order"
    TECHNICIAN = "technician"
    EMAIL_METADATA = "email_metadata"
    TRANSCRIPTION_RESULT = "transcription_result"
    CUSTOMER_DATA = "customer_data"
    EQUIPMENT_DATA = "equipment_data"
    INVOICE_DATA = "invoice_data"


@dataclass
class ValidationRequest:
    """Validation request with metadata."""
    data_type: DataType
    data: Dict[str, Any]
    validation_level: ValidationLevel = ValidationLevel.STANDARD
    source: Optional[str] = None
    request_id: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class ValidationResponse:
    """Comprehensive validation response."""
    request_id: Optional[str]
    data_type: DataType
    is_valid: bool
    validated_data: Dict[str, Any]
    errors: List[str]
    warnings: List[str]
    validation_level: ValidationLevel
    processing_time_ms: float
    source: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class EmailMetadata(BaseModel):
    """Email metadata validation model."""
    sender: str
    subject: str
    received_at: datetime
    has_attachments: bool = False
    attachment_count: int = 0
    attachment_types: List[str] = []
    message_id: Optional[str] = None
    thread_id: Optional[str] = None


class TranscriptionResult(BaseModel):
    """Transcription result validation model."""
    transcription_id: str
    audio_file_path: str
    transcription_text: str
    confidence_score: float
    language: str = "pl"
    processing_time_seconds: float
    model_used: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


class CustomerData(BaseModel):
    """Customer data validation model."""
    name: str
    email: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    company: Optional[str] = None
    nip: Optional[str] = None
    notes: Optional[str] = None


class EquipmentData(BaseModel):
    """Equipment data validation model."""
    model: str
    manufacturer: str
    type: str
    capacity: Optional[str] = None
    energy_class: Optional[str] = None
    installation_date: Optional[datetime] = None
    warranty_until: Optional[datetime] = None
    serial_number: Optional[str] = None


class InvoiceData(BaseModel):
    """Invoice data validation model."""
    invoice_number: str
    issue_date: datetime
    due_date: Optional[datetime] = None
    customer_name: str
    total_amount: float
    currency: str = "PLN"
    items: List[Dict[str, Any]] = []
    tax_amount: Optional[float] = None


class UnifiedValidator:
    """
    Unified validation service that consolidates all validation logic.
    
    Features:
    - Multiple data type support
    - Configurable validation levels
    - Async processing with queuing
    - Comprehensive error reporting
    - Performance metrics
    """
    
    def __init__(self):
        self.hvac_validator = DataValidator()
        self.validation_models = {
            DataType.SERVICE_ORDER: None,  # Uses hvac_validator
            DataType.TECHNICIAN: None,     # Uses hvac_validator
            DataType.EMAIL_METADATA: EmailMetadata,
            DataType.TRANSCRIPTION_RESULT: TranscriptionResult,
            DataType.CUSTOMER_DATA: CustomerData,
            DataType.EQUIPMENT_DATA: EquipmentData,
            DataType.INVOICE_DATA: InvoiceData
        }
        
        # Performance metrics
        self.metrics = {
            "total_validations": 0,
            "successful_validations": 0,
            "failed_validations": 0,
            "validation_times": [],
            "validations_by_type": {},
            "validations_by_level": {}
        }
    
    async def validate(self, request: ValidationRequest) -> ValidationResponse:
        """Validate data according to request specifications."""
        start_time = datetime.now()
        
        try:
            self.metrics["total_validations"] += 1
            self._update_type_metrics(request.data_type)
            self._update_level_metrics(request.validation_level)
            
            # Route to appropriate validator
            if request.data_type in [DataType.SERVICE_ORDER, DataType.TECHNICIAN]:
                result = await self._validate_hvac_data(request)
            else:
                result = await self._validate_pydantic_data(request)
            
            # Calculate processing time
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.metrics["validation_times"].append(processing_time)
            
            # Update success metrics
            if result.is_valid:
                self.metrics["successful_validations"] += 1
            else:
                self.metrics["failed_validations"] += 1
            
            # Create response
            response = ValidationResponse(
                request_id=request.request_id,
                data_type=request.data_type,
                is_valid=result.is_valid,
                validated_data=result.validated_data,
                errors=result.errors,
                warnings=result.warnings,
                validation_level=request.validation_level,
                processing_time_ms=processing_time,
                source=request.source,
                metadata=request.metadata
            )
            
            # Log validation result
            if result.is_valid:
                logger.debug(f"✅ Validation successful for {request.data_type.value} in {processing_time:.1f}ms")
            else:
                logger.warning(f"❌ Validation failed for {request.data_type.value}: {result.errors}")
            
            return response
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds() * 1000
            self.metrics["failed_validations"] += 1
            
            logger.error(f"Validation error for {request.data_type.value}: {e}")
            
            return ValidationResponse(
                request_id=request.request_id,
                data_type=request.data_type,
                is_valid=False,
                validated_data={},
                errors=[f"Validation system error: {str(e)}"],
                warnings=[],
                validation_level=request.validation_level,
                processing_time_ms=processing_time,
                source=request.source,
                metadata=request.metadata
            )
    
    async def validate_batch(self, requests: List[ValidationRequest]) -> List[ValidationResponse]:
        """Validate multiple requests in parallel."""
        logger.info(f"🔄 Processing batch validation of {len(requests)} items")
        
        # Process in parallel
        tasks = [self.validate(request) for request in requests]
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Handle any exceptions
        valid_responses = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                logger.error(f"Batch validation error for item {i}: {response}")
                # Create error response
                error_response = ValidationResponse(
                    request_id=requests[i].request_id,
                    data_type=requests[i].data_type,
                    is_valid=False,
                    validated_data={},
                    errors=[f"Batch processing error: {str(response)}"],
                    warnings=[],
                    validation_level=requests[i].validation_level,
                    processing_time_ms=0.0,
                    source=requests[i].source
                )
                valid_responses.append(error_response)
            else:
                valid_responses.append(response)
        
        logger.success(f"✅ Batch validation completed: {len(valid_responses)} responses")
        return valid_responses
    
    async def queue_validation(self, request: ValidationRequest, priority: QueuePriority = QueuePriority.NORMAL) -> str:
        """Queue validation request for async processing."""
        validation_queue = await queue_manager.get_queue("validation_requests")
        
        message_id = await validation_queue.enqueue(
            payload={
                "validation_request": {
                    "data_type": request.data_type.value,
                    "data": request.data,
                    "validation_level": request.validation_level.value,
                    "source": request.source,
                    "request_id": request.request_id,
                    "metadata": request.metadata
                }
            },
            priority=priority,
            metadata={"type": "validation_request", "data_type": request.data_type.value}
        )
        
        logger.info(f"📤 Queued validation request {request.request_id} as message {message_id}")
        return message_id
    
    async def _validate_hvac_data(self, request: ValidationRequest) -> ValidationResult:
        """Validate HVAC-specific data using the specialized validator."""
        if request.data_type == DataType.SERVICE_ORDER:
            return await self.hvac_validator.validate_service_order(request.data)
        elif request.data_type == DataType.TECHNICIAN:
            return await self.hvac_validator.validate_technician(request.data)
        else:
            raise ValueError(f"Unsupported HVAC data type: {request.data_type}")
    
    async def _validate_pydantic_data(self, request: ValidationRequest) -> ValidationResult:
        """Validate data using Pydantic models."""
        model_class = self.validation_models.get(request.data_type)
        if not model_class:
            raise ValueError(f"No validation model for data type: {request.data_type}")
        
        try:
            # Validate using Pydantic model
            validated_instance = model_class(**request.data)
            validated_data = validated_instance.dict()
            
            # Apply validation level logic
            warnings = []
            if request.validation_level == ValidationLevel.LENIENT:
                # In lenient mode, we might skip some warnings
                pass
            elif request.validation_level == ValidationLevel.STRICT:
                # In strict mode, we might convert warnings to errors
                pass
            
            return ValidationResult(
                is_valid=True,
                validated_data=validated_data,
                errors=[],
                warnings=warnings
            )
            
        except ValidationError as e:
            errors = []
            for error in e.errors():
                field = error.get('loc', ['unknown'])[0]
                message = error.get('msg', 'Validation error')
                errors.append(f"{field}: {message}")
            
            return ValidationResult(
                is_valid=False,
                validated_data={},
                errors=errors,
                warnings=[]
            )
    
    def _update_type_metrics(self, data_type: DataType):
        """Update metrics by data type."""
        type_key = data_type.value
        if type_key not in self.metrics["validations_by_type"]:
            self.metrics["validations_by_type"][type_key] = 0
        self.metrics["validations_by_type"][type_key] += 1
    
    def _update_level_metrics(self, validation_level: ValidationLevel):
        """Update metrics by validation level."""
        level_key = validation_level.value
        if level_key not in self.metrics["validations_by_level"]:
            self.metrics["validations_by_level"][level_key] = 0
        self.metrics["validations_by_level"][level_key] += 1
    
    def get_validation_metrics(self) -> Dict[str, Any]:
        """Get comprehensive validation metrics."""
        total = self.metrics["total_validations"]
        success_rate = (self.metrics["successful_validations"] / max(total, 1)) * 100
        
        avg_time = 0.0
        if self.metrics["validation_times"]:
            avg_time = sum(self.metrics["validation_times"]) / len(self.metrics["validation_times"])
        
        return {
            "total_validations": total,
            "successful_validations": self.metrics["successful_validations"],
            "failed_validations": self.metrics["failed_validations"],
            "success_rate": f"{success_rate:.1f}%",
            "average_processing_time_ms": f"{avg_time:.1f}",
            "validations_by_type": self.metrics["validations_by_type"],
            "validations_by_level": self.metrics["validations_by_level"],
            "hvac_validator_stats": self.hvac_validator.get_validation_statistics()
        }


# Global unified validator instance
unified_validator = UnifiedValidator()
