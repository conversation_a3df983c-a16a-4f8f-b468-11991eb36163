#!/usr/bin/env python3
"""
Persistent Queue System for python_mixer
Redis-based persistent queues with at-least-once delivery guarantees
"""

import asyncio
import json
import time
import uuid
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Callable, Union
from dataclasses import dataclass, asdict
from enum import Enum

import redis.asyncio as redis
from loguru import logger

from ..security import env_loader


class QueuePriority(Enum):
    """Queue priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


class MessageStatus(Enum):
    """Message processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    DEAD_LETTER = "dead_letter"


@dataclass
class QueueMessage:
    """Persistent queue message with metadata."""
    id: str
    queue_name: str
    payload: Dict[str, Any]
    priority: QueuePriority
    created_at: datetime
    scheduled_at: Optional[datetime] = None
    attempts: int = 0
    max_attempts: int = 3
    status: MessageStatus = MessageStatus.PENDING
    error_message: Optional[str] = None
    processing_started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}


class PersistentQueue:
    """Redis-based persistent queue with reliability features."""
    
    def __init__(
        self,
        queue_name: str,
        redis_url: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: int = 60,
        dead_letter_enabled: bool = True
    ):
        self.queue_name = queue_name
        self.redis_url = redis_url or env_loader.get_var("REDIS_URL", "redis://localhost:6379")
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.dead_letter_enabled = dead_letter_enabled
        
        # Redis connection
        self.redis_client: Optional[redis.Redis] = None
        
        # Queue keys
        self.pending_key = f"queue:{queue_name}:pending"
        self.processing_key = f"queue:{queue_name}:processing"
        self.completed_key = f"queue:{queue_name}:completed"
        self.failed_key = f"queue:{queue_name}:failed"
        self.dead_letter_key = f"queue:{queue_name}:dead_letter"
        self.metadata_key = f"queue:{queue_name}:metadata"
        
        # Statistics
        self.stats = {
            "messages_enqueued": 0,
            "messages_processed": 0,
            "messages_failed": 0,
            "messages_dead_lettered": 0
        }
    
    async def initialize(self) -> bool:
        """Initialize Redis connection."""
        try:
            self.redis_client = redis.from_url(
                self.redis_url,
                decode_responses=True,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={}
            )
            
            # Test connection
            await self.redis_client.ping()
            logger.success(f"✅ Persistent queue '{self.queue_name}' connected to Redis")
            
            # Initialize queue statistics
            await self._initialize_stats()
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize persistent queue '{self.queue_name}': {e}")
            return False
    
    async def enqueue(
        self,
        payload: Dict[str, Any],
        priority: QueuePriority = QueuePriority.NORMAL,
        delay_seconds: int = 0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """Enqueue a message with optional delay and priority."""
        if not self.redis_client:
            raise RuntimeError("Queue not initialized")
        
        message_id = str(uuid.uuid4())
        scheduled_at = None
        
        if delay_seconds > 0:
            scheduled_at = datetime.now() + timedelta(seconds=delay_seconds)
        
        message = QueueMessage(
            id=message_id,
            queue_name=self.queue_name,
            payload=payload,
            priority=priority,
            created_at=datetime.now(),
            scheduled_at=scheduled_at,
            metadata=metadata or {}
        )
        
        # Serialize message
        message_data = json.dumps(asdict(message), default=str)
        
        # Store message metadata
        await self.redis_client.hset(
            self.metadata_key,
            message_id,
            message_data
        )
        
        # Add to appropriate queue based on scheduling
        if scheduled_at:
            # Delayed message - use sorted set with timestamp
            score = scheduled_at.timestamp()
            await self.redis_client.zadd(f"{self.pending_key}:delayed", {message_id: score})
        else:
            # Immediate message - use priority queue
            priority_score = priority.value * 1000000 + int(time.time())
            await self.redis_client.zadd(self.pending_key, {message_id: priority_score})
        
        self.stats["messages_enqueued"] += 1
        logger.debug(f"📤 Enqueued message {message_id} to queue '{self.queue_name}'")
        
        return message_id
    
    async def dequeue(self, timeout: int = 30) -> Optional[QueueMessage]:
        """Dequeue the next available message."""
        if not self.redis_client:
            raise RuntimeError("Queue not initialized")
        
        # First, check for delayed messages that are ready
        await self._process_delayed_messages()
        
        # Get highest priority message
        result = await self.redis_client.bzpopmax(self.pending_key, timeout=timeout)
        
        if not result:
            return None
        
        _, message_id, _ = result
        
        # Get message metadata
        message_data = await self.redis_client.hget(self.metadata_key, message_id)
        if not message_data:
            logger.warning(f"Message metadata not found for {message_id}")
            return None
        
        # Deserialize message
        message_dict = json.loads(message_data)
        message_dict['created_at'] = datetime.fromisoformat(message_dict['created_at'])
        if message_dict['scheduled_at']:
            message_dict['scheduled_at'] = datetime.fromisoformat(message_dict['scheduled_at'])
        if message_dict['processing_started_at']:
            message_dict['processing_started_at'] = datetime.fromisoformat(message_dict['processing_started_at'])
        if message_dict['completed_at']:
            message_dict['completed_at'] = datetime.fromisoformat(message_dict['completed_at'])
        
        message_dict['priority'] = QueuePriority(message_dict['priority'])
        message_dict['status'] = MessageStatus(message_dict['status'])
        
        message = QueueMessage(**message_dict)
        
        # Mark as processing
        message.status = MessageStatus.PROCESSING
        message.processing_started_at = datetime.now()
        message.attempts += 1
        
        # Update metadata
        await self._update_message_metadata(message)
        
        # Move to processing queue
        await self.redis_client.zadd(
            self.processing_key,
            {message_id: time.time()}
        )
        
        logger.debug(f"📥 Dequeued message {message_id} from queue '{self.queue_name}'")
        return message
    
    async def complete_message(self, message: QueueMessage) -> bool:
        """Mark message as completed."""
        if not self.redis_client:
            return False
        
        try:
            # Update message status
            message.status = MessageStatus.COMPLETED
            message.completed_at = datetime.now()
            
            # Update metadata
            await self._update_message_metadata(message)
            
            # Remove from processing queue
            await self.redis_client.zrem(self.processing_key, message.id)
            
            # Add to completed queue (with TTL for cleanup)
            await self.redis_client.zadd(
                self.completed_key,
                {message.id: time.time()}
            )
            
            # Set TTL for completed messages (7 days)
            await self.redis_client.expire(self.completed_key, 7 * 24 * 3600)
            
            self.stats["messages_processed"] += 1
            logger.debug(f"✅ Completed message {message.id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to complete message {message.id}: {e}")
            return False
    
    async def fail_message(self, message: QueueMessage, error: str) -> bool:
        """Mark message as failed and handle retry logic."""
        if not self.redis_client:
            return False
        
        try:
            message.error_message = error
            message.status = MessageStatus.FAILED
            
            # Check if we should retry or dead letter
            if message.attempts < message.max_attempts:
                # Retry with exponential backoff
                delay = self.retry_delay * (2 ** (message.attempts - 1))
                scheduled_at = datetime.now() + timedelta(seconds=delay)
                
                message.scheduled_at = scheduled_at
                message.status = MessageStatus.PENDING
                
                # Update metadata
                await self._update_message_metadata(message)
                
                # Remove from processing
                await self.redis_client.zrem(self.processing_key, message.id)
                
                # Add back to delayed queue
                await self.redis_client.zadd(
                    f"{self.pending_key}:delayed",
                    {message.id: scheduled_at.timestamp()}
                )
                
                logger.warning(f"🔄 Retrying message {message.id} in {delay}s (attempt {message.attempts}/{message.max_attempts})")
                
            else:
                # Dead letter the message
                if self.dead_letter_enabled:
                    message.status = MessageStatus.DEAD_LETTER
                    await self._update_message_metadata(message)
                    
                    # Remove from processing
                    await self.redis_client.zrem(self.processing_key, message.id)
                    
                    # Add to dead letter queue
                    await self.redis_client.zadd(
                        self.dead_letter_key,
                        {message.id: time.time()}
                    )
                    
                    self.stats["messages_dead_lettered"] += 1
                    logger.error(f"💀 Dead lettered message {message.id} after {message.attempts} attempts")
                
            self.stats["messages_failed"] += 1
            return True
            
        except Exception as e:
            logger.error(f"Failed to handle message failure {message.id}: {e}")
            return False
    
    async def get_queue_stats(self) -> Dict[str, Any]:
        """Get comprehensive queue statistics."""
        if not self.redis_client:
            return {}
        
        try:
            pending_count = await self.redis_client.zcard(self.pending_key)
            delayed_count = await self.redis_client.zcard(f"{self.pending_key}:delayed")
            processing_count = await self.redis_client.zcard(self.processing_key)
            completed_count = await self.redis_client.zcard(self.completed_key)
            failed_count = await self.redis_client.zcard(self.failed_key)
            dead_letter_count = await self.redis_client.zcard(self.dead_letter_key)
            
            return {
                "queue_name": self.queue_name,
                "pending_messages": pending_count,
                "delayed_messages": delayed_count,
                "processing_messages": processing_count,
                "completed_messages": completed_count,
                "failed_messages": failed_count,
                "dead_letter_messages": dead_letter_count,
                "total_enqueued": self.stats["messages_enqueued"],
                "total_processed": self.stats["messages_processed"],
                "total_failed": self.stats["messages_failed"],
                "total_dead_lettered": self.stats["messages_dead_lettered"],
                "success_rate": (
                    self.stats["messages_processed"] / max(1, self.stats["messages_enqueued"])
                ) * 100
            }
            
        except Exception as e:
            logger.error(f"Failed to get queue stats: {e}")
            return {}
    
    async def _process_delayed_messages(self):
        """Move ready delayed messages to pending queue."""
        if not self.redis_client:
            return
        
        try:
            now = time.time()
            delayed_key = f"{self.pending_key}:delayed"
            
            # Get messages ready for processing
            ready_messages = await self.redis_client.zrangebyscore(
                delayed_key, 0, now, withscores=True
            )
            
            if ready_messages:
                pipe = self.redis_client.pipeline()
                
                for message_id, _ in ready_messages:
                    # Remove from delayed queue
                    pipe.zrem(delayed_key, message_id)
                    
                    # Add to pending queue with current priority
                    priority_score = QueuePriority.NORMAL.value * 1000000 + int(time.time())
                    pipe.zadd(self.pending_key, {message_id: priority_score})
                
                await pipe.execute()
                logger.debug(f"Moved {len(ready_messages)} delayed messages to pending")
                
        except Exception as e:
            logger.error(f"Failed to process delayed messages: {e}")
    
    async def _update_message_metadata(self, message: QueueMessage):
        """Update message metadata in Redis."""
        if not self.redis_client:
            return
        
        message_data = json.dumps(asdict(message), default=str)
        await self.redis_client.hset(
            self.metadata_key,
            message.id,
            message_data
        )
    
    async def _initialize_stats(self):
        """Initialize queue statistics."""
        if not self.redis_client:
            return
        
        stats_key = f"queue:{self.queue_name}:stats"
        existing_stats = await self.redis_client.hgetall(stats_key)
        
        if existing_stats:
            self.stats.update({
                k: int(v) for k, v in existing_stats.items()
                if k in self.stats
            })
    
    async def close(self):
        """Close Redis connection."""
        if self.redis_client:
            await self.redis_client.close()
            logger.info(f"🔄 Closed persistent queue '{self.queue_name}'")


class QueueManager:
    """Manager for multiple persistent queues."""
    
    def __init__(self, redis_url: Optional[str] = None):
        self.redis_url = redis_url or env_loader.get_var("REDIS_URL", "redis://localhost:6379")
        self.queues: Dict[str, PersistentQueue] = {}
    
    async def get_queue(self, queue_name: str, **kwargs) -> PersistentQueue:
        """Get or create a persistent queue."""
        if queue_name not in self.queues:
            queue = PersistentQueue(
                queue_name=queue_name,
                redis_url=self.redis_url,
                **kwargs
            )
            await queue.initialize()
            self.queues[queue_name] = queue
        
        return self.queues[queue_name]
    
    async def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get statistics for all queues."""
        stats = {}
        for queue_name, queue in self.queues.items():
            stats[queue_name] = await queue.get_queue_stats()
        return stats
    
    async def close_all(self):
        """Close all queues."""
        for queue in self.queues.values():
            await queue.close()
        self.queues.clear()


# Global queue manager instance
queue_manager = QueueManager()
