version: '3.8'

# 🚀 Enhanced HVAC CRM System - Technological Advancements Implementation
# Comprehensive Docker Compose for all system improvements

services:
  # ==========================================
  # CORE INFRASTRUCTURE
  # ==========================================
  
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: hvac-postgres
    environment:
      POSTGRES_DB: hvacdb
      POSTGRES_USER: hvacdb
      POSTGRES_PASSWORD: blaeritipol
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./GoBackend-Kratos-fresh/migrations:/docker-entrypoint-initdb.d
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hvacdb"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: hvac-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # NATS Message Queue
  nats:
    image: nats:2.10-alpine
    container_name: hvac-nats
    command: 
      - "--jetstream"
      - "--store_dir=/data"
      - "--max_memory_store=1GB"
      - "--max_file_store=10GB"
    ports:
      - "4222:4222"
      - "8222:8222"  # HTTP monitoring
      - "6222:6222"  # Cluster
    volumes:
      - nats_data:/data
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8222/healthz"]
      interval: 10s
      timeout: 5s
      retries: 5

  # ==========================================
  # MONITORING STACK
  # ==========================================

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: hvac-prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - hvac-network
    depends_on:
      - go-backend

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: hvac-grafana
    ports:
      - "3001:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: false
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - hvac-network
    depends_on:
      - prometheus

  # ==========================================
  # MCP SERVERS
  # ==========================================

  # Memory MCP Server
  mcp-memory:
    image: node:18-alpine
    container_name: hvac-mcp-memory
    working_dir: /app
    command: npx -y @modelcontextprotocol/server-memory
    ports:
      - "3001:3001"
    environment:
      - PORT=3001
      - NODE_ENV=production
    volumes:
      - mcp_memory_data:/app/data
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Tavily MCP Server
  mcp-tavily:
    image: node:18-alpine
    container_name: hvac-mcp-tavily
    working_dir: /app
    command: npx -y @modelcontextprotocol/server-tavily
    ports:
      - "3002:3002"
    environment:
      - PORT=3002
      - TAVILY_API_KEY=${TAVILY_API_KEY}
      - NODE_ENV=production
    networks:
      - hvac-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:3002/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ==========================================
  # CORE SERVICES
  # ==========================================

  # Enhanced Go Backend
  go-backend:
    build:
      context: ./GoBackend-Kratos-fresh
      dockerfile: Dockerfile
    container_name: hvac-go-backend
    ports:
      - "8080:8080"
      - "9000:9000"  # gRPC
    environment:
      # Database
      - DATABASE_URL=*********************************************/hvacdb
      
      # Redis
      - REDIS_URL=redis://redis:6379
      
      # NATS
      - NATS_URL=nats://nats:4222
      - NATS_CLUSTER_ID=hvac-cluster
      - NATS_CLIENT_ID=hvac-backend
      
      # MCP
      - MCP_MEMORY_URL=http://mcp-memory:3001
      - MCP_TAVILY_URL=http://mcp-tavily:3002
      
      # Monitoring
      - PROMETHEUS_ENABLED=true
      - PROMETHEUS_PORT=9090
      
      # Security
      - JWT_SECRET=${JWT_SECRET:-your-super-secret-jwt-key}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY:-your-encryption-key}
      
      # Email Configuration
      - EMAIL_DOLORES_PASSWORD=${EMAIL_DOLORES_PASSWORD:-Blaeritipol1}
      - EMAIL_GRZEGORZ_PASSWORD=${EMAIL_GRZEGORZ_PASSWORD:-Blaeritipol1}
      
      # AI Services
      - LM_STUDIO_URL=http://host.docker.internal:1234
      - GEMMA_MODEL=gemma-3-4b-it-qat
      
    volumes:
      - ./GoBackend-Kratos-fresh/configs:/app/configs
      - go_backend_logs:/app/logs
    networks:
      - hvac-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      nats:
        condition: service_healthy
      mcp-memory:
        condition: service_healthy
      mcp-tavily:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Enhanced Python Mixer
  python-mixer:
    build:
      context: ./python_mixer
      dockerfile: Dockerfile
    container_name: hvac-python-mixer
    ports:
      - "7860:7860"  # Gradio interface
      - "8000:8000"  # FastAPI
    environment:
      # Database
      - DATABASE_URL=*********************************************/hvacdb
      
      # Go Backend Integration
      - GO_BACKEND_URL=http://go-backend:8080
      - GO_BACKEND_API_KEY=${GO_BACKEND_API_KEY}
      
      # MCP Integration
      - MCP_MEMORY_URL=http://mcp-memory:3001
      - MCP_TAVILY_URL=http://mcp-tavily:3002
      
      # Email Configuration
      - EMAIL_DOLORES_PASSWORD=${EMAIL_DOLORES_PASSWORD:-Blaeritipol1}
      - EMAIL_GRZEGORZ_PASSWORD=${EMAIL_GRZEGORZ_PASSWORD:-Blaeritipol1}
      
      # AI Services
      - LM_STUDIO_URL=http://host.docker.internal:1234
      - GEMMA_MODEL=gemma-3-4b-it-qat
      
      # Storage
      - MINIO_ENDPOINT=host.docker.internal:9000
      - MINIO_ACCESS_KEY=koldbringer
      - MINIO_SECRET_KEY=Blaeritipol1
      
    volumes:
      - ./python_mixer:/app
      - python_mixer_data:/app/data
      - python_mixer_logs:/app/logs
    networks:
      - hvac-network
    depends_on:
      go-backend:
        condition: service_healthy
      mcp-memory:
        condition: service_healthy
      mcp-tavily:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860"]
      interval: 30s
      timeout: 10s
      retries: 5

  # ==========================================
  # FRONTEND SERVICES
  # ==========================================

  # SolidJS Frontend
  hvac-frontend:
    build:
      context: ./hvac-solidjs-cosmic
      dockerfile: Dockerfile
    container_name: hvac-frontend
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://go-backend:8080
      - VITE_WS_URL=ws://go-backend:8080
      - NODE_ENV=production
    volumes:
      - ./hvac-solidjs-cosmic:/app
    networks:
      - hvac-network
    depends_on:
      - go-backend

  # ==========================================
  # UTILITY SERVICES
  # ==========================================

  # System Health Monitor
  health-monitor:
    image: alpine:latest
    container_name: hvac-health-monitor
    command: |
      sh -c "
        apk add --no-cache curl wget &&
        while true; do
          echo '🔍 Health Check Report - $(date)'
          echo '================================'
          curl -s http://go-backend:8080/api/health | jq '.' || echo 'Go Backend: UNHEALTHY'
          curl -s http://python-mixer:8000/health | jq '.' || echo 'Python Mixer: UNHEALTHY'
          curl -s http://prometheus:9090/-/healthy || echo 'Prometheus: UNHEALTHY'
          curl -s http://mcp-memory:3001/health || echo 'MCP Memory: UNHEALTHY'
          curl -s http://mcp-tavily:3002/health || echo 'MCP Tavily: UNHEALTHY'
          echo '================================'
          sleep 60
        done
      "
    networks:
      - hvac-network
    depends_on:
      - go-backend
      - python-mixer
      - prometheus

# ==========================================
# NETWORKS AND VOLUMES
# ==========================================

networks:
  hvac-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nats_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  mcp_memory_data:
    driver: local
  go_backend_logs:
    driver: local
  python_mixer_data:
    driver: local
  python_mixer_logs:
    driver: local
