global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'hvac-go-backend'
    static_configs:
      - targets: ['go-backend:9090']
    metrics_path: '/metrics'
    scrape_interval: 10s

  - job_name: 'hvac-python-mixer'
    static_configs:
      - targets: ['python-mixer:8000']
    metrics_path: '/metrics'
    scrape_interval: 15s

  - job_name: 'nats'
    static_configs:
      - targets: ['nats:8222']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres:5432']
    scrape_interval: 30s

  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
