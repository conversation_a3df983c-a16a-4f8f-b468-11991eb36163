package integration

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"

	"GoBackend-Kratos-fresh/internal/mcp"
	"GoBackend-Kratos-fresh/internal/messaging"
	"GoBackend-Kratos-fresh/internal/monitoring"
	"GoBackend-Kratos-fresh/internal/resilience"
	"GoBackend-Kratos-fresh/internal/config"
)

// 🚀 Enhanced Service Orchestrator for HVAC CRM System
// Coordinates all technological advancement components

// ServiceOrchestratorConfig holds configuration for the orchestrator
type ServiceOrchestratorConfig struct {
	// Service configurations
	MCPConfig        *mcp.MCPServiceConfig        `yaml:"mcp"`
	NATSConfig       *messaging.NATSConfig        `yaml:"nats"`
	PrometheusConfig *monitoring.PrometheusConfig `yaml:"prometheus"`
	
	// Orchestrator settings
	StartupTimeout   time.Duration `yaml:"startup_timeout" env:"ORCHESTRATOR_STARTUP_TIMEOUT"`
	ShutdownTimeout  time.Duration `yaml:"shutdown_timeout" env:"ORCHESTRATOR_SHUTDOWN_TIMEOUT"`
	HealthCheckInterval time.Duration `yaml:"health_check_interval" env:"ORCHESTRATOR_HEALTH_INTERVAL"`
	EnableAutoRecovery  bool          `yaml:"enable_auto_recovery" env:"ORCHESTRATOR_AUTO_RECOVERY"`
}

// DefaultServiceOrchestratorConfig returns default configuration
func DefaultServiceOrchestratorConfig() *ServiceOrchestratorConfig {
	return &ServiceOrchestratorConfig{
		StartupTimeout:      30 * time.Second,
		ShutdownTimeout:     15 * time.Second,
		HealthCheckInterval: 30 * time.Second,
		EnableAutoRecovery:  true,
	}
}

// ServiceOrchestrator manages all enhanced services
type ServiceOrchestrator struct {
	config    *ServiceOrchestratorConfig
	logger    *log.Helper

	// Core services
	mcpService        *mcp.MCPService
	natsService       *messaging.NATSService
	prometheusService *monitoring.PrometheusService
	circuitBreakers   *resilience.CircuitBreakerManager

	// State management
	ctx           context.Context
	cancel        context.CancelFunc
	wg            sync.WaitGroup
	mu            sync.RWMutex
	started       bool
	healthStatus  map[string]ServiceHealth
}

// ServiceHealth represents the health status of a service
type ServiceHealth struct {
	Name         string    `json:"name"`
	Status       string    `json:"status"` // healthy, degraded, unhealthy
	LastCheck    time.Time `json:"last_check"`
	ErrorCount   int       `json:"error_count"`
	LastError    string    `json:"last_error,omitempty"`
	Uptime       time.Duration `json:"uptime"`
	Dependencies []string  `json:"dependencies"`
}

// ServiceStatus represents overall system status
type ServiceStatus struct {
	Overall     string                   `json:"overall"`
	Services    map[string]ServiceHealth `json:"services"`
	StartedAt   time.Time               `json:"started_at"`
	Uptime      time.Duration           `json:"uptime"`
	Version     string                  `json:"version"`
	Environment string                  `json:"environment"`
}

// NewServiceOrchestrator creates a new service orchestrator
func NewServiceOrchestrator(config *ServiceOrchestratorConfig, logger log.Logger) *ServiceOrchestrator {
	if config == nil {
		config = DefaultServiceOrchestratorConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	return &ServiceOrchestrator{
		config:          config,
		logger:          log.NewHelper(logger),
		ctx:             ctx,
		cancel:          cancel,
		healthStatus:    make(map[string]ServiceHealth),
		circuitBreakers: resilience.NewCircuitBreakerManager(logger),
	}
}

// Initialize initializes all services
func (so *ServiceOrchestrator) Initialize(ctx context.Context) error {
	so.logger.WithContext(ctx).Info("🚀 Initializing Enhanced Service Orchestrator...")

	// Initialize Prometheus monitoring first
	if err := so.initializePrometheus(ctx); err != nil {
		return fmt.Errorf("failed to initialize Prometheus: %w", err)
	}

	// Initialize NATS messaging
	if err := so.initializeNATS(ctx); err != nil {
		return fmt.Errorf("failed to initialize NATS: %w", err)
	}

	// Initialize MCP service
	if err := so.initializeMCP(ctx); err != nil {
		return fmt.Errorf("failed to initialize MCP: %w", err)
	}

	// Initialize circuit breakers for all services
	so.initializeCircuitBreakers()

	so.logger.WithContext(ctx).Info("✅ Enhanced Service Orchestrator initialized successfully")
	return nil
}

// Start starts all services
func (so *ServiceOrchestrator) Start(ctx context.Context) error {
	so.mu.Lock()
	defer so.mu.Unlock()

	if so.started {
		return fmt.Errorf("service orchestrator already started")
	}

	so.logger.WithContext(ctx).Info("🚀 Starting Enhanced Service Orchestrator...")

	// Start Prometheus monitoring
	if so.prometheusService != nil {
		if err := so.prometheusService.Start(ctx); err != nil {
			return fmt.Errorf("failed to start Prometheus: %w", err)
		}
	}

	// Start NATS consumers
	if so.natsService != nil {
		if err := so.natsService.StartConsumers(); err != nil {
			return fmt.Errorf("failed to start NATS consumers: %w", err)
		}
	}

	// Start health monitoring
	so.wg.Add(1)
	go so.healthMonitor()

	// Start auto-recovery if enabled
	if so.config.EnableAutoRecovery {
		so.wg.Add(1)
		go so.autoRecoveryMonitor()
	}

	so.started = true
	so.logger.WithContext(ctx).Info("✅ Enhanced Service Orchestrator started successfully")

	// Record startup metrics
	if so.prometheusService != nil {
		so.prometheusService.SetActiveConnections(1)
	}

	return nil
}

// Stop stops all services gracefully
func (so *ServiceOrchestrator) Stop(ctx context.Context) error {
	so.mu.Lock()
	defer so.mu.Unlock()

	if !so.started {
		return nil
	}

	so.logger.WithContext(ctx).Info("🔄 Stopping Enhanced Service Orchestrator...")

	// Cancel context to stop all goroutines
	so.cancel()

	// Wait for all goroutines to finish
	done := make(chan struct{})
	go func() {
		so.wg.Wait()
		close(done)
	}()

	select {
	case <-done:
		so.logger.WithContext(ctx).Info("All goroutines stopped")
	case <-time.After(so.config.ShutdownTimeout):
		so.logger.WithContext(ctx).Warn("Shutdown timeout reached, forcing stop")
	}

	// Stop services in reverse order
	if so.prometheusService != nil {
		if err := so.prometheusService.Stop(ctx); err != nil {
			so.logger.WithContext(ctx).Errorf("Error stopping Prometheus: %v", err)
		}
	}

	if so.natsService != nil {
		if err := so.natsService.Close(); err != nil {
			so.logger.WithContext(ctx).Errorf("Error stopping NATS: %v", err)
		}
	}

	if so.mcpService != nil {
		if err := so.mcpService.Close(ctx); err != nil {
			so.logger.WithContext(ctx).Errorf("Error stopping MCP: %v", err)
		}
	}

	so.started = false
	so.logger.WithContext(ctx).Info("✅ Enhanced Service Orchestrator stopped")
	return nil
}

// initializePrometheus initializes the Prometheus monitoring service
func (so *ServiceOrchestrator) initializePrometheus(ctx context.Context) error {
	if so.config.PrometheusConfig == nil {
		so.config.PrometheusConfig = monitoring.DefaultPrometheusConfig()
	}

	so.prometheusService = monitoring.NewPrometheusService(so.config.PrometheusConfig, so.logger)
	so.logger.WithContext(ctx).Info("📊 Prometheus monitoring service initialized")
	return nil
}

// initializeNATS initializes the NATS messaging service
func (so *ServiceOrchestrator) initializeNATS(ctx context.Context) error {
	if so.config.NATSConfig == nil {
		so.config.NATSConfig = messaging.DefaultNATSConfig()
	}

	natsService, err := messaging.NewNATSService(so.config.NATSConfig, so.logger)
	if err != nil {
		return fmt.Errorf("failed to create NATS service: %w", err)
	}

	so.natsService = natsService
	so.logger.WithContext(ctx).Info("📡 NATS messaging service initialized")
	return nil
}

// initializeMCP initializes the MCP service
func (so *ServiceOrchestrator) initializeMCP(ctx context.Context) error {
	if so.config.MCPConfig == nil {
		so.config.MCPConfig = &mcp.MCPServiceConfig{
			MemoryServerURL: "http://localhost:3001",
			TavilyServerURL: "http://localhost:3002",
			Timeout:         30 * time.Second,
			RetryAttempts:   3,
			EnableMetrics:   true,
		}
	}

	so.mcpService = mcp.NewMCPService(so.config.MCPConfig, so.logger)
	
	if err := so.mcpService.Initialize(ctx); err != nil {
		return fmt.Errorf("failed to initialize MCP service: %w", err)
	}

	so.logger.WithContext(ctx).Info("🧠 MCP service initialized")
	return nil
}

// initializeCircuitBreakers initializes circuit breakers for all services
func (so *ServiceOrchestrator) initializeCircuitBreakers() {
	// Create circuit breakers for each service
	services := []string{
		resilience.CBTranscriptionService,
		resilience.CBEmailService,
		resilience.CBDatabaseService,
		resilience.CBSTTService,
		resilience.CBMCPService,
		resilience.CBNATSService,
	}

	for _, serviceName := range services {
		config := resilience.DefaultCircuitBreakerConfig(serviceName)
		config.OnStateChange = so.onCircuitBreakerStateChange
		so.circuitBreakers.GetOrCreate(serviceName, config)
	}

	so.logger.Info("🔧 Circuit breakers initialized for all services")
}

// onCircuitBreakerStateChange handles circuit breaker state changes
func (so *ServiceOrchestrator) onCircuitBreakerStateChange(name string, from, to resilience.CircuitBreakerState) {
	so.logger.Infof("🔧 Circuit breaker %s state changed: %s -> %s", name, from.String(), to.String())
	
	// Record metrics
	if so.prometheusService != nil {
		so.prometheusService.SetCircuitBreakerState(name, int(to))
	}

	// Update health status
	so.mu.Lock()
	if health, exists := so.healthStatus[name]; exists {
		if to == resilience.StateOpen {
			health.Status = "unhealthy"
			health.ErrorCount++
		} else if to == resilience.StateClosed {
			health.Status = "healthy"
		} else {
			health.Status = "degraded"
		}
		so.healthStatus[name] = health
	}
	so.mu.Unlock()
}

// healthMonitor monitors the health of all services
func (so *ServiceOrchestrator) healthMonitor() {
	defer so.wg.Done()

	ticker := time.NewTicker(so.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-so.ctx.Done():
			return
		case <-ticker.C:
			so.performHealthChecks()
		}
	}
}

// performHealthChecks performs health checks on all services
func (so *ServiceOrchestrator) performHealthChecks() {
	so.mu.Lock()
	defer so.mu.Unlock()

	now := time.Now()

	// Check NATS health
	if so.natsService != nil {
		natsHealth := so.natsService.Health()
		status := "healthy"
		if connected, ok := natsHealth["connected"].(bool); !ok || !connected {
			status = "unhealthy"
		}

		so.healthStatus["nats"] = ServiceHealth{
			Name:      "nats",
			Status:    status,
			LastCheck: now,
			Uptime:    time.Since(now), // This would be calculated properly
		}
	}

	// Check MCP health
	if so.mcpService != nil {
		mcpMetrics := so.mcpService.GetMetrics()
		status := "healthy"
		if mcpMetrics.FailedOps > mcpMetrics.SuccessfulOps {
			status = "degraded"
		}

		so.healthStatus["mcp"] = ServiceHealth{
			Name:      "mcp",
			Status:    status,
			LastCheck: now,
			Uptime:    time.Since(now),
		}
	}

	// Update Prometheus metrics
	if so.prometheusService != nil {
		healthyCount := 0
		totalCount := len(so.healthStatus)
		
		for _, health := range so.healthStatus {
			if health.Status == "healthy" {
				healthyCount++
			}
		}

		// Record overall system health
		if totalCount > 0 {
			healthRatio := float64(healthyCount) / float64(totalCount)
			so.prometheusService.SetActiveConnections(healthRatio)
		}
	}
}

// autoRecoveryMonitor monitors for failed services and attempts recovery
func (so *ServiceOrchestrator) autoRecoveryMonitor() {
	defer so.wg.Done()

	ticker := time.NewTicker(60 * time.Second) // Check every minute
	defer ticker.Stop()

	for {
		select {
		case <-so.ctx.Done():
			return
		case <-ticker.C:
			so.attemptAutoRecovery()
		}
	}
}

// attemptAutoRecovery attempts to recover failed services
func (so *ServiceOrchestrator) attemptAutoRecovery() {
	so.mu.RLock()
	defer so.mu.RUnlock()

	for serviceName, health := range so.healthStatus {
		if health.Status == "unhealthy" && health.ErrorCount < 3 {
			so.logger.Infof("🔄 Attempting auto-recovery for service: %s", serviceName)
			
			// Reset circuit breaker for the service
			if cb, exists := so.circuitBreakers.Get(serviceName); exists {
				cb.Reset()
				so.logger.Infof("✅ Circuit breaker reset for service: %s", serviceName)
			}
		}
	}
}

// GetStatus returns the current status of all services
func (so *ServiceOrchestrator) GetStatus() ServiceStatus {
	so.mu.RLock()
	defer so.mu.RUnlock()

	overall := "healthy"
	for _, health := range so.healthStatus {
		if health.Status == "unhealthy" {
			overall = "unhealthy"
			break
		} else if health.Status == "degraded" && overall == "healthy" {
			overall = "degraded"
		}
	}

	return ServiceStatus{
		Overall:     overall,
		Services:    so.healthStatus,
		StartedAt:   time.Now(), // This would be stored properly
		Uptime:      time.Since(time.Now()),
		Version:     "1.0.0",
		Environment: "production",
	}
}

// GetMCPService returns the MCP service instance
func (so *ServiceOrchestrator) GetMCPService() *mcp.MCPService {
	return so.mcpService
}

// GetNATSService returns the NATS service instance
func (so *ServiceOrchestrator) GetNATSService() *messaging.NATSService {
	return so.natsService
}

// GetPrometheusService returns the Prometheus service instance
func (so *ServiceOrchestrator) GetPrometheusService() *monitoring.PrometheusService {
	return so.prometheusService
}

// GetCircuitBreakerManager returns the circuit breaker manager
func (so *ServiceOrchestrator) GetCircuitBreakerManager() *resilience.CircuitBreakerManager {
	return so.circuitBreakers
}
