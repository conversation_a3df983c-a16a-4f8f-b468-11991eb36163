package monitoring

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// 🚀 Enhanced Prometheus Monitoring Service for HVAC CRM
// Provides comprehensive metrics collection and monitoring

// PrometheusConfig holds Prometheus configuration
type PrometheusConfig struct {
	Enabled   bool   `yaml:"enabled" env:"PROMETHEUS_ENABLED"`
	Port      int    `yaml:"port" env:"PROMETHEUS_PORT"`
	Path      string `yaml:"path" env:"PROMETHEUS_PATH"`
	Namespace string `yaml:"namespace" env:"PROMETHEUS_NAMESPACE"`
	Subsystem string `yaml:"subsystem" env:"PROMETHEUS_SUBSYSTEM"`
}

// DefaultPrometheusConfig returns default configuration
func DefaultPrometheusConfig() *PrometheusConfig {
	return &PrometheusConfig{
		Enabled:   true,
		Port:      9090,
		Path:      "/metrics",
		Namespace: "hvac",
		Subsystem: "crm",
	}
}

// PrometheusService manages Prometheus metrics collection
type PrometheusService struct {
	config   *PrometheusConfig
	registry *prometheus.Registry
	server   *http.Server
	logger   *log.Helper

	// Core metrics
	requestsTotal     *prometheus.CounterVec
	requestDuration   *prometheus.HistogramVec
	activeConnections prometheus.Gauge
	systemUptime      prometheus.Gauge

	// Transcription metrics
	transcriptionJobsTotal *prometheus.CounterVec
	transcriptionDuration  *prometheus.HistogramVec
	transcriptionErrors    *prometheus.CounterVec
	transcriptionQueueSize prometheus.Gauge
	transcriptionAccuracy  *prometheus.HistogramVec

	// Email metrics
	emailsProcessedTotal    *prometheus.CounterVec
	emailProcessingDuration *prometheus.HistogramVec
	emailAttachmentsTotal   *prometheus.CounterVec
	emailErrors             *prometheus.CounterVec

	// Circuit breaker metrics
	circuitBreakerState    *prometheus.GaugeVec
	circuitBreakerRequests *prometheus.CounterVec
	circuitBreakerFailures *prometheus.CounterVec

	// NATS metrics
	natsMessagesPublished *prometheus.CounterVec
	natsMessagesReceived  *prometheus.CounterVec
	natsConnectionStatus  *prometheus.GaugeVec

	// MCP metrics
	mcpOperationsTotal   *prometheus.CounterVec
	mcpOperationDuration *prometheus.HistogramVec
	mcpConnectionStatus  *prometheus.GaugeVec
}

// NewPrometheusService creates a new Prometheus service
func NewPrometheusService(config *PrometheusConfig, logger log.Logger) *PrometheusService {
	if config == nil {
		config = DefaultPrometheusConfig()
	}

	registry := prometheus.NewRegistry()

	service := &PrometheusService{
		config:   config,
		registry: registry,
		logger:   log.NewHelper(logger),
	}

	service.initializeMetrics()
	service.registerMetrics()

	return service
}

// initializeMetrics initializes all Prometheus metrics
func (ps *PrometheusService) initializeMetrics() {
	// Core metrics
	ps.requestsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: ps.config.Subsystem,
			Name:      "requests_total",
			Help:      "Total number of HTTP requests",
		},
		[]string{"method", "endpoint", "status"},
	)

	ps.requestDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: ps.config.Namespace,
			Subsystem: ps.config.Subsystem,
			Name:      "request_duration_seconds",
			Help:      "HTTP request duration in seconds",
			Buckets:   prometheus.DefBuckets,
		},
		[]string{"method", "endpoint"},
	)

	ps.activeConnections = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Namespace: ps.config.Namespace,
			Subsystem: ps.config.Subsystem,
			Name:      "active_connections",
			Help:      "Number of active connections",
		},
	)

	ps.systemUptime = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Namespace: ps.config.Namespace,
			Subsystem: ps.config.Subsystem,
			Name:      "system_uptime_seconds",
			Help:      "System uptime in seconds",
		},
	)

	// Transcription metrics
	ps.transcriptionJobsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "transcription",
			Name:      "jobs_total",
			Help:      "Total number of transcription jobs",
		},
		[]string{"status", "source"},
	)

	ps.transcriptionDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "transcription",
			Name:      "duration_seconds",
			Help:      "Transcription processing duration in seconds",
			Buckets:   []float64{1, 5, 10, 30, 60, 120, 300},
		},
		[]string{"source", "model"},
	)

	ps.transcriptionErrors = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "transcription",
			Name:      "errors_total",
			Help:      "Total number of transcription errors",
		},
		[]string{"error_type", "source"},
	)

	ps.transcriptionQueueSize = prometheus.NewGauge(
		prometheus.GaugeOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "transcription",
			Name:      "queue_size",
			Help:      "Current transcription queue size",
		},
	)

	ps.transcriptionAccuracy = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "transcription",
			Name:      "accuracy_score",
			Help:      "Transcription accuracy score",
			Buckets:   []float64{0.5, 0.6, 0.7, 0.8, 0.85, 0.9, 0.95, 0.99, 1.0},
		},
		[]string{"model", "language"},
	)

	// Email metrics
	ps.emailsProcessedTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "email",
			Name:      "processed_total",
			Help:      "Total number of emails processed",
		},
		[]string{"source", "status"},
	)

	ps.emailProcessingDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "email",
			Name:      "processing_duration_seconds",
			Help:      "Email processing duration in seconds",
			Buckets:   prometheus.DefBuckets,
		},
		[]string{"source", "type"},
	)

	ps.emailAttachmentsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "email",
			Name:      "attachments_total",
			Help:      "Total number of email attachments processed",
		},
		[]string{"type", "status"},
	)

	ps.emailErrors = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "email",
			Name:      "errors_total",
			Help:      "Total number of email processing errors",
		},
		[]string{"error_type", "source"},
	)

	// Circuit breaker metrics
	ps.circuitBreakerState = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "circuit_breaker",
			Name:      "state",
			Help:      "Circuit breaker state (0=closed, 1=half-open, 2=open)",
		},
		[]string{"name"},
	)

	ps.circuitBreakerRequests = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "circuit_breaker",
			Name:      "requests_total",
			Help:      "Total number of circuit breaker requests",
		},
		[]string{"name", "status"},
	)

	ps.circuitBreakerFailures = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "circuit_breaker",
			Name:      "failures_total",
			Help:      "Total number of circuit breaker failures",
		},
		[]string{"name"},
	)

	// NATS metrics
	ps.natsMessagesPublished = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "nats",
			Name:      "messages_published_total",
			Help:      "Total number of NATS messages published",
		},
		[]string{"subject"},
	)

	ps.natsMessagesReceived = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "nats",
			Name:      "messages_received_total",
			Help:      "Total number of NATS messages received",
		},
		[]string{"subject"},
	)

	ps.natsConnectionStatus = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "nats",
			Name:      "connection_status",
			Help:      "NATS connection status (1=connected, 0=disconnected)",
		},
		[]string{"server"},
	)

	// MCP metrics
	ps.mcpOperationsTotal = prometheus.NewCounterVec(
		prometheus.CounterOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "mcp",
			Name:      "operations_total",
			Help:      "Total number of MCP operations",
		},
		[]string{"server", "operation", "status"},
	)

	ps.mcpOperationDuration = prometheus.NewHistogramVec(
		prometheus.HistogramOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "mcp",
			Name:      "operation_duration_seconds",
			Help:      "MCP operation duration in seconds",
			Buckets:   prometheus.DefBuckets,
		},
		[]string{"server", "operation"},
	)

	ps.mcpConnectionStatus = prometheus.NewGaugeVec(
		prometheus.GaugeOpts{
			Namespace: ps.config.Namespace,
			Subsystem: "mcp",
			Name:      "connection_status",
			Help:      "MCP connection status (1=connected, 0=disconnected)",
		},
		[]string{"server"},
	)
}

// registerMetrics registers all metrics with the registry
func (ps *PrometheusService) registerMetrics() {
	// Core metrics
	ps.registry.MustRegister(ps.requestsTotal)
	ps.registry.MustRegister(ps.requestDuration)
	ps.registry.MustRegister(ps.activeConnections)
	ps.registry.MustRegister(ps.systemUptime)

	// Transcription metrics
	ps.registry.MustRegister(ps.transcriptionJobsTotal)
	ps.registry.MustRegister(ps.transcriptionDuration)
	ps.registry.MustRegister(ps.transcriptionErrors)
	ps.registry.MustRegister(ps.transcriptionQueueSize)
	ps.registry.MustRegister(ps.transcriptionAccuracy)

	// Email metrics
	ps.registry.MustRegister(ps.emailsProcessedTotal)
	ps.registry.MustRegister(ps.emailProcessingDuration)
	ps.registry.MustRegister(ps.emailAttachmentsTotal)
	ps.registry.MustRegister(ps.emailErrors)

	// Circuit breaker metrics
	ps.registry.MustRegister(ps.circuitBreakerState)
	ps.registry.MustRegister(ps.circuitBreakerRequests)
	ps.registry.MustRegister(ps.circuitBreakerFailures)

	// NATS metrics
	ps.registry.MustRegister(ps.natsMessagesPublished)
	ps.registry.MustRegister(ps.natsMessagesReceived)
	ps.registry.MustRegister(ps.natsConnectionStatus)

	// MCP metrics
	ps.registry.MustRegister(ps.mcpOperationsTotal)
	ps.registry.MustRegister(ps.mcpOperationDuration)
	ps.registry.MustRegister(ps.mcpConnectionStatus)
}

// Start starts the Prometheus metrics server
func (ps *PrometheusService) Start(ctx context.Context) error {
	if !ps.config.Enabled {
		ps.logger.Info("Prometheus monitoring is disabled")
		return nil
	}

	mux := http.NewServeMux()
	mux.Handle(ps.config.Path, promhttp.HandlerFor(ps.registry, promhttp.HandlerOpts{}))

	ps.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", ps.config.Port),
		Handler: mux,
	}

	ps.logger.Infof("🚀 Starting Prometheus metrics server on port %d", ps.config.Port)

	go func() {
		if err := ps.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			ps.logger.Errorf("Prometheus server error: %v", err)
		}
	}()

	// Start system uptime tracking
	go ps.trackSystemUptime(ctx)

	ps.logger.Info("✅ Prometheus monitoring service started")
	return nil
}

// Stop stops the Prometheus metrics server
func (ps *PrometheusService) Stop(ctx context.Context) error {
	if ps.server == nil {
		return nil
	}

	ps.logger.Info("🔄 Stopping Prometheus monitoring service...")

	shutdownCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	if err := ps.server.Shutdown(shutdownCtx); err != nil {
		return fmt.Errorf("failed to shutdown Prometheus server: %w", err)
	}

	ps.logger.Info("✅ Prometheus monitoring service stopped")
	return nil
}

// trackSystemUptime tracks system uptime
func (ps *PrometheusService) trackSystemUptime(ctx context.Context) {
	startTime := time.Now()
	ticker := time.NewTicker(10 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			uptime := time.Since(startTime).Seconds()
			ps.systemUptime.Set(uptime)
		}
	}
}

// Metric recording methods

// RecordHTTPRequest records HTTP request metrics
func (ps *PrometheusService) RecordHTTPRequest(method, endpoint, status string, duration time.Duration) {
	ps.requestsTotal.WithLabelValues(method, endpoint, status).Inc()
	ps.requestDuration.WithLabelValues(method, endpoint).Observe(duration.Seconds())
}

// RecordTranscriptionJob records transcription job metrics
func (ps *PrometheusService) RecordTranscriptionJob(status, source string, duration time.Duration, accuracy float64, model string) {
	ps.transcriptionJobsTotal.WithLabelValues(status, source).Inc()
	ps.transcriptionDuration.WithLabelValues(source, model).Observe(duration.Seconds())
	if accuracy > 0 {
		ps.transcriptionAccuracy.WithLabelValues(model, "pl").Observe(accuracy)
	}
}

// RecordTranscriptionError records transcription error metrics
func (ps *PrometheusService) RecordTranscriptionError(errorType, source string) {
	ps.transcriptionErrors.WithLabelValues(errorType, source).Inc()
}

// SetTranscriptionQueueSize sets the current transcription queue size
func (ps *PrometheusService) SetTranscriptionQueueSize(size float64) {
	ps.transcriptionQueueSize.Set(size)
}

// RecordEmailProcessing records email processing metrics
func (ps *PrometheusService) RecordEmailProcessing(source, status string, duration time.Duration, emailType string) {
	ps.emailsProcessedTotal.WithLabelValues(source, status).Inc()
	ps.emailProcessingDuration.WithLabelValues(source, emailType).Observe(duration.Seconds())
}

// RecordEmailAttachment records email attachment metrics
func (ps *PrometheusService) RecordEmailAttachment(attachmentType, status string) {
	ps.emailAttachmentsTotal.WithLabelValues(attachmentType, status).Inc()
}

// RecordEmailError records email processing error metrics
func (ps *PrometheusService) RecordEmailError(errorType, source string) {
	ps.emailErrors.WithLabelValues(errorType, source).Inc()
}

// SetCircuitBreakerState sets circuit breaker state metrics
func (ps *PrometheusService) SetCircuitBreakerState(name string, state int) {
	ps.circuitBreakerState.WithLabelValues(name).Set(float64(state))
}

// RecordCircuitBreakerRequest records circuit breaker request metrics
func (ps *PrometheusService) RecordCircuitBreakerRequest(name, status string) {
	ps.circuitBreakerRequests.WithLabelValues(name, status).Inc()
}

// RecordCircuitBreakerFailure records circuit breaker failure metrics
func (ps *PrometheusService) RecordCircuitBreakerFailure(name string) {
	ps.circuitBreakerFailures.WithLabelValues(name).Inc()
}

// RecordNATSMessage records NATS message metrics
func (ps *PrometheusService) RecordNATSMessagePublished(subject string) {
	ps.natsMessagesPublished.WithLabelValues(subject).Inc()
}

// RecordNATSMessageReceived records NATS message received metrics
func (ps *PrometheusService) RecordNATSMessageReceived(subject string) {
	ps.natsMessagesReceived.WithLabelValues(subject).Inc()
}

// SetNATSConnectionStatus sets NATS connection status
func (ps *PrometheusService) SetNATSConnectionStatus(server string, connected bool) {
	status := 0.0
	if connected {
		status = 1.0
	}
	ps.natsConnectionStatus.WithLabelValues(server).Set(status)
}

// RecordMCPOperation records MCP operation metrics
func (ps *PrometheusService) RecordMCPOperation(server, operation, status string, duration time.Duration) {
	ps.mcpOperationsTotal.WithLabelValues(server, operation, status).Inc()
	ps.mcpOperationDuration.WithLabelValues(server, operation).Observe(duration.Seconds())
}

// SetMCPConnectionStatus sets MCP connection status
func (ps *PrometheusService) SetMCPConnectionStatus(server string, connected bool) {
	status := 0.0
	if connected {
		status = 1.0
	}
	ps.mcpConnectionStatus.WithLabelValues(server).Set(status)
}

// SetActiveConnections sets the number of active connections
func (ps *PrometheusService) SetActiveConnections(count float64) {
	ps.activeConnections.Set(count)
}
