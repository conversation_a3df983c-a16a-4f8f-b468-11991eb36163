package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
	"github.com/metoro-io/mcp-golang"
	"github.com/metoro-io/mcp-golang/transport"
)

// 🚀 Enhanced MCP Service for HVAC CRM System
// Provides comprehensive MCP integration with Memory and Tavily servers

// MCPServiceConfig holds configuration for MCP service
type MCPServiceConfig struct {
	MemoryServerURL string `yaml:"memory_server_url" env:"MCP_MEMORY_URL"`
	TavilyServerURL string `yaml:"tavily_server_url" env:"MCP_TAVILY_URL"`
	Timeout         time.Duration `yaml:"timeout" env:"MCP_TIMEOUT"`
	RetryAttempts   int    `yaml:"retry_attempts" env:"MCP_RETRY_ATTEMPTS"`
	EnableMetrics   bool   `yaml:"enable_metrics" env:"MCP_ENABLE_METRICS"`
}

// MCPService manages MCP client connections and operations
type MCPService struct {
	config       *MCPServiceConfig
	memoryClient *mcp_golang.Client
	tavilyClient *mcp_golang.Client
	logger       *log.Helper
	mu           sync.RWMutex
	connected    bool
	metrics      *MCPMetrics
}

// MCPMetrics tracks MCP operation metrics
type MCPMetrics struct {
	TotalOperations   int64     `json:"total_operations"`
	SuccessfulOps     int64     `json:"successful_operations"`
	FailedOps         int64     `json:"failed_operations"`
	LastOperation     time.Time `json:"last_operation"`
	AverageLatency    float64   `json:"average_latency_ms"`
	ConnectionUptime  time.Duration `json:"connection_uptime"`
	mu                sync.RWMutex
}

// EntityData represents data for MCP entity operations
type EntityData struct {
	Name         string                 `json:"name"`
	EntityType   string                 `json:"entityType"`
	Observations map[string]interface{} `json:"observations"`
}

// RelationData represents data for MCP relation operations
type RelationData struct {
	From         string                 `json:"from"`
	To           string                 `json:"to"`
	RelationType string                 `json:"relationType"`
	Observations map[string]interface{} `json:"observations"`
}

// NewMCPService creates a new MCP service instance
func NewMCPService(config *MCPServiceConfig, logger log.Logger) *MCPService {
	return &MCPService{
		config:  config,
		logger:  log.NewHelper(logger),
		metrics: &MCPMetrics{},
	}
}

// Initialize establishes connections to MCP servers
func (s *MCPService) Initialize(ctx context.Context) error {
	s.logger.WithContext(ctx).Info("🔌 Initializing MCP Service...")

	// Initialize Memory Server connection
	if err := s.initializeMemoryClient(ctx); err != nil {
		return fmt.Errorf("failed to initialize memory client: %w", err)
	}

	// Initialize Tavily Server connection
	if err := s.initializeTavilyClient(ctx); err != nil {
		return fmt.Errorf("failed to initialize tavily client: %w", err)
	}

	s.mu.Lock()
	s.connected = true
	s.mu.Unlock()

	s.logger.WithContext(ctx).Info("✅ MCP Service initialized successfully")
	return nil
}

// initializeMemoryClient sets up the memory server client
func (s *MCPService) initializeMemoryClient(ctx context.Context) error {
	if s.config.MemoryServerURL == "" {
		return fmt.Errorf("memory server URL not configured")
	}

	// Create HTTP transport for memory server
	transport := transport.NewHTTPTransport(s.config.MemoryServerURL)
	
	// Create MCP client
	client := mcp_golang.NewClient(transport)
	
	// Initialize client
	if err := client.Initialize(ctx, mcp_golang.ClientInfo{
		Name:    "hvac-crm-backend",
		Version: "1.0.0",
	}); err != nil {
		return fmt.Errorf("failed to initialize memory client: %w", err)
	}

	s.memoryClient = client
	s.logger.WithContext(ctx).Info("🧠 Memory MCP client initialized")
	return nil
}

// initializeTavilyClient sets up the tavily server client
func (s *MCPService) initializeTavilyClient(ctx context.Context) error {
	if s.config.TavilyServerURL == "" {
		return fmt.Errorf("tavily server URL not configured")
	}

	// Create HTTP transport for tavily server
	transport := transport.NewHTTPTransport(s.config.TavilyServerURL)
	
	// Create MCP client
	client := mcp_golang.NewClient(transport)
	
	// Initialize client
	if err := client.Initialize(ctx, mcp_golang.ClientInfo{
		Name:    "hvac-crm-backend",
		Version: "1.0.0",
	}); err != nil {
		return fmt.Errorf("failed to initialize tavily client: %w", err)
	}

	s.tavilyClient = client
	s.logger.WithContext(ctx).Info("🔍 Tavily MCP client initialized")
	return nil
}

// CreateEntity creates an entity in the memory server
func (s *MCPService) CreateEntity(ctx context.Context, entity EntityData) error {
	if !s.isConnected() {
		return fmt.Errorf("MCP service not connected")
	}

	startTime := time.Now()
	defer s.recordMetrics(startTime, true)

	// Prepare entity data
	entityJSON, err := json.Marshal(entity)
	if err != nil {
		s.recordMetrics(startTime, false)
		return fmt.Errorf("failed to marshal entity: %w", err)
	}

	// Call MCP tool
	result, err := s.memoryClient.CallTool(ctx, "create_entities", map[string]interface{}{
		"entities": []interface{}{entity},
	})
	if err != nil {
		s.recordMetrics(startTime, false)
		return fmt.Errorf("failed to create entity: %w", err)
	}

	s.logger.WithContext(ctx).Infof("✅ Entity created: %s (type: %s)", entity.Name, entity.EntityType)
	s.logger.WithContext(ctx).Debugf("MCP Result: %+v", result)
	
	return nil
}

// CreateRelation creates a relation in the memory server
func (s *MCPService) CreateRelation(ctx context.Context, relation RelationData) error {
	if !s.isConnected() {
		return fmt.Errorf("MCP service not connected")
	}

	startTime := time.Now()
	defer s.recordMetrics(startTime, true)

	// Call MCP tool
	result, err := s.memoryClient.CallTool(ctx, "create_relations", map[string]interface{}{
		"relations": []interface{}{relation},
	})
	if err != nil {
		s.recordMetrics(startTime, false)
		return fmt.Errorf("failed to create relation: %w", err)
	}

	s.logger.WithContext(ctx).Infof("✅ Relation created: %s -> %s (%s)", 
		relation.From, relation.To, relation.RelationType)
	s.logger.WithContext(ctx).Debugf("MCP Result: %+v", result)
	
	return nil
}

// SearchWithTavily performs a search using Tavily MCP server
func (s *MCPService) SearchWithTavily(ctx context.Context, query string, options map[string]interface{}) (interface{}, error) {
	if !s.isConnected() {
		return nil, fmt.Errorf("MCP service not connected")
	}

	startTime := time.Now()
	defer s.recordMetrics(startTime, true)

	// Prepare search parameters
	params := map[string]interface{}{
		"query": query,
	}
	
	// Add optional parameters
	for k, v := range options {
		params[k] = v
	}

	// Call Tavily search tool
	result, err := s.tavilyClient.CallTool(ctx, "search", params)
	if err != nil {
		s.recordMetrics(startTime, false)
		return nil, fmt.Errorf("failed to search with tavily: %w", err)
	}

	s.logger.WithContext(ctx).Infof("🔍 Tavily search completed for query: %s", query)
	
	return result, nil
}

// GetMetrics returns current MCP service metrics
func (s *MCPService) GetMetrics() *MCPMetrics {
	s.metrics.mu.RLock()
	defer s.metrics.mu.RUnlock()
	
	// Create a copy to avoid race conditions
	return &MCPMetrics{
		TotalOperations:  s.metrics.TotalOperations,
		SuccessfulOps:    s.metrics.SuccessfulOps,
		FailedOps:        s.metrics.FailedOps,
		LastOperation:    s.metrics.LastOperation,
		AverageLatency:   s.metrics.AverageLatency,
		ConnectionUptime: s.metrics.ConnectionUptime,
	}
}

// isConnected checks if the service is connected
func (s *MCPService) isConnected() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.connected
}

// recordMetrics records operation metrics
func (s *MCPService) recordMetrics(startTime time.Time, success bool) {
	s.metrics.mu.Lock()
	defer s.metrics.mu.Unlock()
	
	latency := time.Since(startTime).Milliseconds()
	s.metrics.TotalOperations++
	s.metrics.LastOperation = time.Now()
	
	if success {
		s.metrics.SuccessfulOps++
	} else {
		s.metrics.FailedOps++
	}
	
	// Update average latency
	if s.metrics.TotalOperations == 1 {
		s.metrics.AverageLatency = float64(latency)
	} else {
		s.metrics.AverageLatency = (s.metrics.AverageLatency + float64(latency)) / 2
	}
}

// Close closes all MCP connections
func (s *MCPService) Close(ctx context.Context) error {
	s.logger.WithContext(ctx).Info("🔌 Closing MCP Service...")
	
	s.mu.Lock()
	s.connected = false
	s.mu.Unlock()
	
	// Close clients if they exist
	// Note: The mcp-golang library doesn't expose a Close method in the current version
	// This would need to be implemented when the library supports it
	
	s.logger.WithContext(ctx).Info("✅ MCP Service closed")
	return nil
}
