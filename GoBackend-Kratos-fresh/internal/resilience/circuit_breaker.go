package resilience

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"time"

	"github.com/go-kratos/kratos/v2/log"
)

// 🚀 Enhanced Circuit Breaker for HVAC CRM System
// Provides resilient error handling for transcription and email services

// CircuitBreakerState represents the current state of the circuit breaker
type CircuitBreakerState int

const (
	StateClosed CircuitBreakerState = iota
	StateHalfOpen
	StateOpen
)

func (s CircuitBreakerState) String() string {
	switch s {
	case StateClosed:
		return "CLOSED"
	case StateHalfOpen:
		return "HALF_OPEN"
	case StateOpen:
		return "OPEN"
	default:
		return "UNKNOWN"
	}
}

// CircuitBreakerConfig holds configuration for circuit breaker
type CircuitBreakerConfig struct {
	Name                string        `yaml:"name"`
	MaxFailures         int           `yaml:"max_failures"`
	Timeout             time.Duration `yaml:"timeout"`
	MaxRequests         int           `yaml:"max_requests"`
	Interval            time.Duration `yaml:"interval"`
	OnStateChange       func(name string, from, to CircuitBreakerState)
	IsSuccessful        func(err error) bool
}

// DefaultCircuitBreakerConfig returns default configuration
func DefaultCircuitBreakerConfig(name string) *CircuitBreakerConfig {
	return &CircuitBreakerConfig{
		Name:         name,
		MaxFailures:  5,
		Timeout:      60 * time.Second,
		MaxRequests:  3,
		Interval:     10 * time.Second,
		IsSuccessful: func(err error) bool { return err == nil },
	}
}

// CircuitBreaker implements the circuit breaker pattern
type CircuitBreaker struct {
	config       *CircuitBreakerConfig
	state        CircuitBreakerState
	failures     int
	requests     int
	lastFailTime time.Time
	nextRetry    time.Time
	logger       *log.Helper
	mu           sync.RWMutex
	metrics      *CircuitBreakerMetrics
}

// CircuitBreakerMetrics tracks circuit breaker metrics
type CircuitBreakerMetrics struct {
	TotalRequests     int64     `json:"total_requests"`
	SuccessfulRequests int64     `json:"successful_requests"`
	FailedRequests    int64     `json:"failed_requests"`
	CircuitOpenCount  int64     `json:"circuit_open_count"`
	LastStateChange   time.Time `json:"last_state_change"`
	CurrentState      string    `json:"current_state"`
	mu                sync.RWMutex
}

// NewCircuitBreaker creates a new circuit breaker instance
func NewCircuitBreaker(config *CircuitBreakerConfig, logger log.Logger) *CircuitBreaker {
	if config == nil {
		config = DefaultCircuitBreakerConfig("default")
	}

	cb := &CircuitBreaker{
		config:  config,
		state:   StateClosed,
		logger:  log.NewHelper(logger),
		metrics: &CircuitBreakerMetrics{
			CurrentState: StateClosed.String(),
		},
	}

	return cb
}

// Execute executes the given function with circuit breaker protection
func (cb *CircuitBreaker) Execute(ctx context.Context, fn func() error) error {
	if err := cb.beforeRequest(); err != nil {
		return err
	}

	startTime := time.Now()
	err := fn()
	
	cb.afterRequest(err)
	cb.recordMetrics(err, time.Since(startTime))
	
	return err
}

// ExecuteWithFallback executes the function with fallback on failure
func (cb *CircuitBreaker) ExecuteWithFallback(ctx context.Context, fn func() error, fallback func() error) error {
	err := cb.Execute(ctx, fn)
	if err != nil && cb.state == StateOpen {
		cb.logger.Warnf("Circuit breaker %s is open, executing fallback", cb.config.Name)
		return fallback()
	}
	return err
}

// beforeRequest checks if the request should be allowed
func (cb *CircuitBreaker) beforeRequest() error {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	now := time.Now()

	switch cb.state {
	case StateClosed:
		return nil
	case StateOpen:
		if now.After(cb.nextRetry) {
			cb.setState(StateHalfOpen)
			cb.requests = 0
			return nil
		}
		return ErrCircuitBreakerOpen
	case StateHalfOpen:
		if cb.requests >= cb.config.MaxRequests {
			return ErrCircuitBreakerOpen
		}
		cb.requests++
		return nil
	}

	return nil
}

// afterRequest handles the result of the request
func (cb *CircuitBreaker) afterRequest(err error) {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	if cb.config.IsSuccessful(err) {
		cb.onSuccess()
	} else {
		cb.onFailure()
	}
}

// onSuccess handles successful requests
func (cb *CircuitBreaker) onSuccess() {
	switch cb.state {
	case StateClosed:
		cb.failures = 0
	case StateHalfOpen:
		cb.setState(StateClosed)
		cb.failures = 0
		cb.requests = 0
	}
}

// onFailure handles failed requests
func (cb *CircuitBreaker) onFailure() {
	cb.failures++
	cb.lastFailTime = time.Now()

	switch cb.state {
	case StateClosed:
		if cb.failures >= cb.config.MaxFailures {
			cb.setState(StateOpen)
			cb.nextRetry = time.Now().Add(cb.config.Timeout)
		}
	case StateHalfOpen:
		cb.setState(StateOpen)
		cb.nextRetry = time.Now().Add(cb.config.Timeout)
	}
}

// setState changes the circuit breaker state
func (cb *CircuitBreaker) setState(state CircuitBreakerState) {
	if cb.state == state {
		return
	}

	oldState := cb.state
	cb.state = state

	cb.logger.Infof("Circuit breaker %s state changed: %s -> %s", 
		cb.config.Name, oldState.String(), state.String())

	// Update metrics
	cb.metrics.mu.Lock()
	cb.metrics.LastStateChange = time.Now()
	cb.metrics.CurrentState = state.String()
	if state == StateOpen {
		cb.metrics.CircuitOpenCount++
	}
	cb.metrics.mu.Unlock()

	// Call state change callback if configured
	if cb.config.OnStateChange != nil {
		go cb.config.OnStateChange(cb.config.Name, oldState, state)
	}
}

// recordMetrics records operation metrics
func (cb *CircuitBreaker) recordMetrics(err error, duration time.Duration) {
	cb.metrics.mu.Lock()
	defer cb.metrics.mu.Unlock()

	cb.metrics.TotalRequests++
	if cb.config.IsSuccessful(err) {
		cb.metrics.SuccessfulRequests++
	} else {
		cb.metrics.FailedRequests++
	}
}

// GetState returns the current state of the circuit breaker
func (cb *CircuitBreaker) GetState() CircuitBreakerState {
	cb.mu.RLock()
	defer cb.mu.RUnlock()
	return cb.state
}

// GetMetrics returns current circuit breaker metrics
func (cb *CircuitBreaker) GetMetrics() *CircuitBreakerMetrics {
	cb.metrics.mu.RLock()
	defer cb.metrics.mu.RUnlock()

	return &CircuitBreakerMetrics{
		TotalRequests:      cb.metrics.TotalRequests,
		SuccessfulRequests: cb.metrics.SuccessfulRequests,
		FailedRequests:     cb.metrics.FailedRequests,
		CircuitOpenCount:   cb.metrics.CircuitOpenCount,
		LastStateChange:    cb.metrics.LastStateChange,
		CurrentState:       cb.metrics.CurrentState,
	}
}

// Reset resets the circuit breaker to closed state
func (cb *CircuitBreaker) Reset() {
	cb.mu.Lock()
	defer cb.mu.Unlock()

	cb.setState(StateClosed)
	cb.failures = 0
	cb.requests = 0
	cb.lastFailTime = time.Time{}
	cb.nextRetry = time.Time{}

	cb.logger.Infof("Circuit breaker %s has been reset", cb.config.Name)
}

// CircuitBreakerManager manages multiple circuit breakers
type CircuitBreakerManager struct {
	breakers map[string]*CircuitBreaker
	logger   *log.Helper
	mu       sync.RWMutex
}

// NewCircuitBreakerManager creates a new circuit breaker manager
func NewCircuitBreakerManager(logger log.Logger) *CircuitBreakerManager {
	return &CircuitBreakerManager{
		breakers: make(map[string]*CircuitBreaker),
		logger:   log.NewHelper(logger),
	}
}

// GetOrCreate gets an existing circuit breaker or creates a new one
func (cbm *CircuitBreakerManager) GetOrCreate(name string, config *CircuitBreakerConfig) *CircuitBreaker {
	cbm.mu.Lock()
	defer cbm.mu.Unlock()

	if cb, exists := cbm.breakers[name]; exists {
		return cb
	}

	if config == nil {
		config = DefaultCircuitBreakerConfig(name)
	}
	config.Name = name

	cb := NewCircuitBreaker(config, cbm.logger)
	cbm.breakers[name] = cb

	cbm.logger.Infof("Created new circuit breaker: %s", name)
	return cb
}

// Get retrieves a circuit breaker by name
func (cbm *CircuitBreakerManager) Get(name string) (*CircuitBreaker, bool) {
	cbm.mu.RLock()
	defer cbm.mu.RUnlock()

	cb, exists := cbm.breakers[name]
	return cb, exists
}

// GetAllMetrics returns metrics for all circuit breakers
func (cbm *CircuitBreakerManager) GetAllMetrics() map[string]*CircuitBreakerMetrics {
	cbm.mu.RLock()
	defer cbm.mu.RUnlock()

	metrics := make(map[string]*CircuitBreakerMetrics)
	for name, cb := range cbm.breakers {
		metrics[name] = cb.GetMetrics()
	}

	return metrics
}

// ResetAll resets all circuit breakers
func (cbm *CircuitBreakerManager) ResetAll() {
	cbm.mu.RLock()
	defer cbm.mu.RUnlock()

	for _, cb := range cbm.breakers {
		cb.Reset()
	}

	cbm.logger.Info("All circuit breakers have been reset")
}

// Common circuit breaker names for HVAC system
const (
	CBTranscriptionService = "transcription-service"
	CBEmailService        = "email-service"
	CBDatabaseService     = "database-service"
	CBSTTService          = "stt-service"
	CBMCPService          = "mcp-service"
	CBNATSService         = "nats-service"
)

// Predefined errors
var (
	ErrCircuitBreakerOpen = errors.New("circuit breaker is open")
	ErrMaxRequestsExceeded = errors.New("maximum requests exceeded in half-open state")
)
