import { Link } from "@remix-run/react";

export function Footer({ className }: { className?: string }) {
  const currentYear = new Date().getFullYear();

  return (
    <footer className={`border-t border-border/40 bg-background/95 backdrop-blur-sm ${className}`}>
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-center gap-6">
          <div className="flex flex-col items-center md:items-start gap-2">
            <div className="font-heading font-bold text-xl text-gradient">HVAC CRM</div>
            <span className="text-sm text-muted-foreground">
              &copy; {currentYear} HVAC CRM. All rights reserved.
            </span>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-x-12 gap-y-2 md:gap-6">
            <Link
              to="/about"
              className="text-sm text-muted-foreground hover:text-accent transition-colors duration-200"
            >
              About
            </Link>
            <Link
              to="/privacy"
              className="text-sm text-muted-foreground hover:text-accent transition-colors duration-200"
            >
              Privacy
            </Link>
            <Link
              to="/terms"
              className="text-sm text-muted-foreground hover:text-accent transition-colors duration-200"
            >
              Terms
            </Link>
            <Link
              to="/contact"
              className="text-sm text-muted-foreground hover:text-accent transition-colors duration-200"
            >
              Contact
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}