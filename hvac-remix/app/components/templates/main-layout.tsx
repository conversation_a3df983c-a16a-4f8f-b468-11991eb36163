import type { User } from "@prisma/client";
import { Footer } from "~/components/organisms/footer";
import { Head<PERSON> } from "~/components/organisms/header";
import { Navigation } from "~/components/organisms/navigation";
import { OfflineIndicator, OfflineBanner } from "~/components/ui/offline-indicator";
import type { UserRole, Notification } from "~/types/shared";

interface MainLayoutProps {
  user: User | null;
  children: React.ReactNode;
  userRole?: UserRole;
  notifications?: Notification[];
  onMarkAsRead?: (id: string) => void;
  onMarkAllAsRead?: () => void;
}

export function MainLayout({
  user,
  children,
  userRole = 'USER',
  notifications = [],
  onMarkAsRead,
  onMarkAllAsRead
}: MainLayoutProps) {
  return (
    <div className="grid min-h-screen grid-rows-[auto_1fr_auto] grid-cols-[1fr] md:grid-cols-[1fr_auto] bg-gradient-to-b from-background to-background/95">
      {/* Offline Banner */}
      <OfflineBanner className="col-span-full" />

      {/* Header */}
      <Header
        user={user}
        userRole={userRole}
        notifications={notifications}
        onMarkAsRead={onMarkAsRead}
        onMarkAllAsRead={onMarkAllAsRead}
        className="col-span-full"
      />

      {/* Main Content */}
      <main className="flex-1 bg-gradient-to-b from-background/50 to-background/80 backdrop-blur-[2px] pt-6 md:col-start-1 md:row-start-2">
        <div className="mx-auto w-full max-w-[1920px] px-4">
          {children}
        </div>
      </main>

      {/* Sidebar */}
      <aside className="md:col-start-2 md:row-start-2 bg-card/50 border-l border-border/40 p-4 hidden md:block">
        <Navigation userRole={userRole} className="flex flex-col space-y-2" />
      </aside>

      {/* Offline Indicator */}
      <div className="fixed bottom-4 right-4 z-50">
        <OfflineIndicator />
      </div>

      {/* Footer */}
      <Footer className="col-span-full" />
    </div>
  );
}